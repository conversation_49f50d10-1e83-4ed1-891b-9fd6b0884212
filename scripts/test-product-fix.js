#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the product fix for features creation
 * This script will test if the product with ID '1753619e-a945-4353-ba7b-806316e82bb8' exists
 * and if features can be created successfully
 */

const BASE_URL = 'http://localhost:7007';

async function testProductFix() {
  console.log('🧪 Testing Product Fix for Features Creation\n');

  try {
    // Test 1: Check if products exist
    console.log('1. Testing GET /api/product/products (checking if products are seeded)');
    const productsResponse = await fetch(`${BASE_URL}/api/product/products`);

    if (productsResponse.ok) {
      const products = await productsResponse.json();
      console.log('✅ GET products successful:', JSON.stringify(products, null, 2));

      // Check if the specific product ID exists
      const targetProductId = '1753619e-a945-4353-ba7b-806316e82bb8';
      const targetProduct = products.data.find(product => product.id === targetProductId);

      if (targetProduct) {
        console.log(`✅ Target product found: ${targetProduct.name} (${targetProductId})`);
      } else {
        console.log(`❌ Target product ${targetProductId} not found`);
        console.log('Available products:', products.data.map(p => `${p.name} (${p.id})`));
      }
    } else {
      console.log('❌ GET products failed:', productsResponse.status, await productsResponse.text());
    }

    // Test 2: Try to create a feature with the specific product ID
    console.log('\n2. Testing POST /api/features/features (creating feature with target product)');
    const createFeatureResponse = await fetch(`${BASE_URL}/api/features/features`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test Feature - Product Fix',
        description: 'This is a test feature to verify the product fix works',
        productId: '1753619e-a945-4353-ba7b-806316e82bb8',
        status: 'active'
      }),
    });

    if (createFeatureResponse.ok) {
      const feature = await createFeatureResponse.json();
      console.log('✅ POST feature successful:', JSON.stringify(feature, null, 2));
      console.log('🎉 Product fix is working! Feature created successfully.');
    } else {
      const errorData = await createFeatureResponse.json().catch(() => ({}));
      console.log('❌ POST feature failed:', createFeatureResponse.status);
      console.log('Error details:', JSON.stringify(errorData, null, 2));

      if (errorData.message && errorData.message.includes('Product with ID') && errorData.message.includes('not found')) {
        console.log('🔍 This is the original error - product seeding may not have worked');
      }
    }

    // Test 3: Get all features to see what was created
    console.log('\n3. Testing GET /api/features/features (checking all features)');
    const featuresResponse = await fetch(`${BASE_URL}/api/features/features`);
    
    if (featuresResponse.ok) {
      const features = await featuresResponse.json();
      console.log('✅ GET features successful:');
      console.log(`Found ${features.data.length} features`);
      features.data.forEach((feature, index) => {
        console.log(`  ${index + 1}. ${feature.name} (${feature.id}) - Product: ${feature.product?.name || 'Unknown'}`);
      });
    } else {
      console.log('❌ GET features failed:', featuresResponse.status, await featuresResponse.text());
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Make sure the Backstage backend is running on http://localhost:7007');
    console.log('   You can start it with: yarn dev');
  }
}

console.log(`
🎯 Product Fix Test Script

This script tests if the product seeding fix resolves the error:
"Product with ID '1753619e-a945-4353-ba7b-806316e82bb8' not found"

The fix includes:
1. Automatic seeding of default products including the target ID
2. Better error handling with helpful suggestions
3. A products endpoint for debugging

Running tests...
`);

testProductFix();
