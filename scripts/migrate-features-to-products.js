#!/usr/bin/env node

/**
 * Migration script to convert features from department-based to product-based
 * 
 * Usage:
 *   node scripts/migrate-features-to-products.js [--dry-run] [--help]
 * 
 * Options:
 *   --dry-run    Run migration without making changes (default: false)
 *   --help       Show this help message
 */

const { createLogger } = require('winston');
const knex = require('knex');

// Simple logger setup
const logger = createLogger({
  level: 'info',
  format: require('winston').format.combine(
    require('winston').format.timestamp(),
    require('winston').format.printf(({ timestamp, level, message, ...meta }) => {
      const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
      return `${timestamp} [${level.toUpperCase()}] ${message}${metaStr}`;
    })
  ),
  transports: [
    new (require('winston').transports.Console)()
  ]
});

// Parse command line arguments
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run');
const showHelp = args.includes('--help');

if (showHelp) {
  console.log(`
Migration script to convert features from department-based to product-based

Usage:
  node scripts/migrate-features-to-products.js [--dry-run] [--help]

Options:
  --dry-run    Run migration without making changes (default: false)
  --help       Show this help message

This script will:
1. Copy all departments to the products table
2. Add product_id column to features table
3. Copy department_id values to product_id
4. Add necessary constraints

Note: This script assumes you're running it from the backstage app root directory.
`);
  process.exit(0);
}

async function runMigration() {
  let database;
  
  try {
    logger.info('Starting features to products migration', { dryRun });

    // Database configuration (adjust as needed for your setup)
    database = knex({
      client: 'better-sqlite3',
      connection: {
        filename: ':memory:', // This should match your actual database configuration
      },
      useNullAsDefault: true,
    });

    // Note: In a real scenario, you would import the migration function
    // For this script, we'll implement a simplified version
    
    logger.info('Checking database tables...');
    
    // Check if tables exist
    const hasDepartments = await database.schema.hasTable('departments');
    const hasProducts = await database.schema.hasTable('products');
    const hasFeatures = await database.schema.hasTable('features');
    
    logger.info('Table status:', { hasDepartments, hasProducts, hasFeatures });
    
    if (!hasDepartments) {
      logger.warn('Departments table not found - nothing to migrate');
      return;
    }
    
    if (!hasProducts) {
      logger.error('Products table not found - cannot migrate');
      process.exit(1);
    }
    
    if (!hasFeatures) {
      logger.warn('Features table not found - nothing to migrate');
      return;
    }

    await database.transaction(async (trx) => {
      // Step 1: Migrate departments to products
      const departments = await trx('departments').select('*');
      logger.info(`Found ${departments.length} departments to migrate`);
      
      let migratedDepartments = 0;
      for (const dept of departments) {
        const existingProduct = await trx('products').where('id', dept.id).first();
        
        if (existingProduct) {
          logger.warn(`Product with ID ${dept.id} already exists, skipping`);
          continue;
        }
        
        if (!dryRun) {
          await trx('products').insert({
            id: dept.id,
            name: dept.name,
            description: dept.description,
            created_at: dept.created_at,
            updated_at: dept.updated_at,
          });
        }
        
        migratedDepartments++;
        logger.info(`${dryRun ? '[DRY RUN] ' : ''}Migrated department: ${dept.name}`);
      }

      // Step 2: Check features table structure
      const tableInfo = await trx.raw("PRAGMA table_info(features)");
      const columns = tableInfo.map(col => col.name);
      
      const hasDepartmentId = columns.includes('department_id');
      const hasProductId = columns.includes('product_id');
      
      logger.info('Features table columns:', { hasDepartmentId, hasProductId });
      
      if (hasDepartmentId && !hasProductId) {
        // Add product_id column
        if (!dryRun) {
          await trx.schema.alterTable('features', (table) => {
            table.string('product_id');
          });
        }
        logger.info(`${dryRun ? '[DRY RUN] ' : ''}Added product_id column to features table`);
        
        // Copy department_id to product_id
        const features = await trx('features').select('id', 'department_id');
        logger.info(`Found ${features.length} features to update`);
        
        for (const feature of features) {
          if (!dryRun) {
            await trx('features')
              .where('id', feature.id)
              .update({ product_id: feature.department_id });
          }
        }
        
        logger.info(`${dryRun ? '[DRY RUN] ' : ''}Updated ${features.length} features with product_id`);
        
        // Add constraints
        if (!dryRun) {
          await trx.schema.alterTable('features', (table) => {
            table.unique(['name', 'product_id']);
          });
        }
        logger.info(`${dryRun ? '[DRY RUN] ' : ''}Added unique constraint on (name, product_id)`);
        
      } else if (hasProductId) {
        logger.info('Features table already has product_id column');
      }
      
      logger.info('Migration completed successfully', {
        migratedDepartments,
        dryRun
      });
      
      if (dryRun) {
        logger.info('This was a dry run - no changes were made to the database');
      } else {
        logger.info('Migration completed! You may now remove the department_id column manually if desired');
      }
    });

  } catch (error) {
    logger.error('Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (database) {
      await database.destroy();
    }
  }
}

// Run the migration
runMigration().catch((error) => {
  logger.error('Unexpected error:', error);
  process.exit(1);
});
