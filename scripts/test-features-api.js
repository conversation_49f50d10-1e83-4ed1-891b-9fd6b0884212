#!/usr/bin/env node

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:7007';

// Mock authentication token (for testing purposes)
const AUTH_TOKEN = 'Bearer test-token';

async function testAPI() {
  console.log('Testing Features API...\n');

  try {
    // Test 1: Get all features (should return empty array initially)
    console.log('1. Testing GET /api/features/features');
    const featuresResponse = await fetch(`${BASE_URL}/api/features/features`, {
      headers: {
        'Authorization': AUTH_TOKEN,
        'Content-Type': 'application/json',
      },
    });
    
    if (featuresResponse.ok) {
      const features = await featuresResponse.json();
      console.log('✅ GET features successful:', features);
    } else {
      console.log('❌ GET features failed:', featuresResponse.status, await featuresResponse.text());
    }

    // Test 2: Get all departments (should work if departments exist)
    console.log('\n2. Testing GET /api/department/departments');
    const departmentsResponse = await fetch(`${BASE_URL}/api/department/departments`, {
      headers: {
        'Authorization': AUTH_TOKEN,
        'Content-Type': 'application/json',
      },
    });
    
    if (departmentsResponse.ok) {
      const departments = await departmentsResponse.json();
      console.log('✅ GET departments successful:', departments);
    } else {
      console.log('❌ GET departments failed:', departmentsResponse.status, await departmentsResponse.text());
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAPI();
