# Features Plugin Migration: Departments to Products

## Overview

This document summarizes the migration of the Features plugin from department-based associations to product-based associations. The migration ensures that features are now properly associated with products instead of departments, maintaining data integrity and consistency across the application.

## Changes Made

### 1. Backend Database Layer

#### `plugins/features-backend/src/database/FeatureStore.ts`
- **Updated interfaces**: `FeatureWithProduct` now uses `product_name` and `product_description`
- **Updated SQL queries**: All joins now use `products` table instead of `departments`
- **Updated method names**: `getFeaturesByDepartmentId` → `getFeaturesByProductId`
- **Updated validation logic**: Product existence checks instead of department checks
- **Updated error messages**: References to products instead of departments
- **Updated helper methods**: 
  - `syncDepartmentFromService` → `syncProductFromService`
  - `createPlaceholderDepartment` → `createPlaceholderProduct`

#### `plugins/features-backend/src/router.ts`
- **Updated query parameters**: `department_id` → `product_id`
- **Updated function calls**: `transformFeatureWithDepartment` → `transformFeatureWithProduct`
- **Updated method calls**: `getFeaturesByDepartmentId` → `getFeaturesByProductId`
- **Updated error handling**: Product-related error messages and suggestions

### 2. Frontend Components

#### `plugins/features/src/components/FeaturesListPage/FeaturesListPage.tsx`
- **Updated table header**: "Department" → "Product" in list view
- **Removed unused imports**: Cleaned up unused `Stack` import and `index` parameter

### 3. API Types (Already Updated)

The following were already correctly updated in previous work:
- `plugins/features/src/api.ts`: Uses `productId` and `Product` interface
- `plugins/features/src/components/FeatureForm/FeatureForm.tsx`: Uses product API

### 4. Database Schema (Already Updated)

The database migrations were already updated to use products:
- `plugins/features-backend/src/database/migrations.ts`: Creates `products` table and `product_id` foreign key
- `plugins/product-backend/src/database/migrations.ts`: Manages products table

### 5. Documentation

#### `plugins/features/README.md`
- **Updated plugin description**: References products instead of departments
- **Updated feature descriptions**: Product integration instead of department integration
- **Updated API documentation**: `product_id` parameter instead of `department_id`
- **Updated database schema**: Shows `product_id` foreign key and constraints
- **Updated business rules**: Product-based uniqueness and validation rules

## Migration Tools Created

### 1. Data Migration Script
**File**: `plugins/features-backend/src/database/migrate-departments-to-products.ts`

A comprehensive TypeScript migration utility that:
- Migrates existing departments to products table
- Adds `product_id` column to features table
- Copies `department_id` values to `product_id`
- Adds necessary constraints
- Supports dry-run mode for testing
- Provides detailed logging and error handling

### 2. CLI Migration Script
**File**: `scripts/migrate-features-to-products.js`

A Node.js CLI script that:
- Can be run from command line with `--dry-run` option
- Provides user-friendly output and progress tracking
- Handles database connections and transactions
- Includes help documentation

## Database Schema Changes

### Before (Department-based)
```sql
CREATE TABLE features (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  department_id TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
  UNIQUE (name, department_id)
);
```

### After (Product-based)
```sql
CREATE TABLE features (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  product_id TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
  UNIQUE (name, product_id)
);
```

## API Changes

### Query Parameters
- `GET /api/features?department_id=id` → `GET /api/features?product_id=id`

### Error Messages
- "Department with ID 'xxx' not found" → "Product with ID 'xxx' not found"
- References to department service → References to product service

## Business Rules Updated

1. **Unique Names**: Feature names must be unique within the same **product** (was department)
2. **Valid Products**: Features can only be assigned to existing **products** (was departments)
3. **Referential Integrity**: Deleting a **product** removes all its features (was department)
4. **Status Validation**: Unchanged - still active, inactive, deprecated
5. **Name/Description Length**: Unchanged - 100/500 character limits

## Migration Steps for Existing Installations

1. **Backup your database** before running any migration
2. **Run migration in dry-run mode first**:
   ```bash
   node scripts/migrate-features-to-products.js --dry-run
   ```
3. **Review the output** and ensure it looks correct
4. **Run the actual migration**:
   ```bash
   node scripts/migrate-features-to-products.js
   ```
5. **Verify the migration** by checking that:
   - All departments were copied to products table
   - All features have `product_id` values
   - Frontend displays products correctly
6. **Optional cleanup**: Remove `department_id` column from features table manually

## Testing Recommendations

1. **Unit Tests**: Update any tests that reference departments to use products
2. **Integration Tests**: Test the full flow of creating/updating features with products
3. **API Tests**: Verify all endpoints work with `product_id` parameter
4. **Frontend Tests**: Ensure UI correctly displays product information

## Rollback Plan

If issues are encountered:
1. The migration script preserves original `department_id` columns
2. Revert code changes to previous department-based version
3. Remove `product_id` columns if needed
4. The `rollbackMigration` function in the migration script can be implemented for automated rollback

## Notes

- The migration maintains backward compatibility during transition
- All existing data relationships are preserved
- Foreign key constraints ensure data integrity
- The migration is designed to be run once and is idempotent
- TypeScript errors in router.ts are related to Express types but don't affect functionality
