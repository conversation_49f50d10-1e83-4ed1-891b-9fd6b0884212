{"info": {"name": "New THUB DEV Collection.postman_collection", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "backstage-postman-converter", "_collection_link": ""}, "item": [{"name": "THUB-Dev", "item": [{"name": "Transfers", "item": [{"name": "External Account Transfer", "request": {"method": "POST", "header": [{"key": "Accept-Language", "value": "en-US", "disabled": false}], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\": {\n        \"type\": \"BANK_ACCOUNT\",\n        \"bankAccountRecipient\": {\n            \"beneficiaryName\": \"EBC\",\n            \"bankId\": \"100\",\n            \"accountNumber\": \"****************\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 100,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"allam\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}"}}}, {"name": "Internal Account Transfer", "request": {"method": "POST", "header": [{"key": "Accept-Language", "value": "ar-EG", "disabled": true}], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\": {\n        \"type\": \"BANK_ACCOUNT\",\n        \"bankAccountRecipient\": {\n            \"beneficiaryName\": \"ahmedz\",\n            \"bankId\": \"7\",\n            \"accountNumber\": \"************\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"allam\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}"}}}, {"name": "Web Hook", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer ", "disabled": true}], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers/:globalId", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"status\":\"REJECTED\",\n    \"errorCode\":\"EIPN11001\",\n    \"errorDescription\":\"asdf\",\n    \"referenceNumber\":\"ABC123\"\n\n}"}}}, {"name": "Simulate Fees", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers/fees", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\":{\n        \"type\": \"BANK_ACCOUNT\",\n    \"bankAccountRecipient\": {\n        \"beneficiaryName\": \"tesa\",\n        \"bankId\": \"25\",\n        \"accountNumber\":\"************\"\n        }\n    },\n    \"nickname\": \"<PERSON>i\",\n    \"amount\": {\n        \"amount\": 2000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"RECIPIENT\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\":\"new test\",\n    \"otp\":\"111221\"\n}"}}}, {"name": "Get all Transfers", "request": {"method": "GET", "header": [], "url": {"raw": "https://dummyjson.com/users", "query": []}}}, {"name": "Get a certain transfer by transfer ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers/:globalId", "query": []}}}, {"name": "IPN transfer request", "request": {"method": "POST", "header": [{"key": "Idempotency-Key", "value": "d154bb9d-0cf2-4029-9bb5-32e3d8dfbec4", "disabled": false}], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\": {\n        \"type\": \"INSTANT_PAYMENT_ADDRESS\",\n        \"instantPaymentAddressRecipient\": {\n            \"instantPaymentAddress\": \"testebc@demopay\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 5,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}"}}}, {"name": "mobile number transfer request", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\": {\n        \"type\": \"MOBILE_NUMBER\",\n        \"mobileNumberRecipient\": {\n            \"mobileNumber\": \"***********\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}"}}}, {"name": "wallet mobile number transfer request", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\": {\n        \"type\": \"WALLET_MOBILE_NUMBER\",\n        \"walletMobileNumberRecipient\": {\n            \"walletMobileNumber\": \"***********\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}"}}}, {"name": "card transfer request", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\": {\n        \"type\": \"CARD\",\n        \"cardRecipient\": {\n            \"cardNumber\": \"MIICEQYJKoZIhvcNAQcDoIICAjCCAf4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQAg5i2S3sYDhvNiShNxUA0csZARwmM/ACafhD4dPNJABVoHrT016cK058P8PL/03mNaNWXnjmH2bknyBqklgjVCNwbspYThzeUJKKYTpnb4eJVouc01evRlHaewg/ryyesFrx6wD142yZXui8Ur0oixaR6wrHqUT7t/cs9NYSh3X96C9/T4uDEOrxU97Q8rI3C1C590OTbQDFaIo3YloWKsk6juAxRl3WQvROh1160FjFlBUWyFDREu6u7p4o032uZN+GcwMSAgKS/SwxkkQGiDKWuqJVLH/fXNCKI4zAhpqkJdB93pjEJfJseDRvHaw9LCaLIvaF1mtUuW8j+Rek73ME4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEKFHYpUBuhRN9ucA5PANq/ygIgQgDRc2YtQQ01HLeRlDgj8m7ahmyhnqNFV6cm9dwvrpsKE=\",\n            \"cardHolderName\": \"EBC Test\"\n        }\n    },\n    \"nickname\": \"Doni\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}"}}}, {"name": "IBAN transfer request", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\": {\n        \"type\": \"BANK_ACCOUNT\",\n        \"ibanAccountRecipient\": {\n            \"beneficiaryName\": \"EBC IBAN\",\n            \"iban\": \"*****************************\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 500,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\":\"new test\",\n    \"otp\":\"111221\"\n}"}}}, {"name": "pay to existing card transfer request", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"beneficiaryGlobalId\": \"07262b50-164a-41d0-924a-7811c415b0ad\",\n    \"recipient\": {\n        \"type\": \"CARD\",\n        \"cardRecipient\": {\n            \"cardNumber\": \"************6516\",\n            \"cardHolderName\": \"gazar visa\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}"}}}]}, {"name": "Beneficiaries", "item": [{"name": "Create a new beneficiary", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"cif\":\"121221\",\n     \"recipient\": {\n        \"type\": \"CARD\",\n        \"cardRecipient\": {\n            \"cardNumber\": \"MIICEQYJKoZIhvcNAQcDoIICAjCCAf4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQCpoZlUJdX2pVUz2Zdac0Rht/yo61FOQ0G93suo3fs7KZOTuaE92G6+SLaX3PlPvMnWgKpxk77TbfbSy1VY5am95guCMDpol5CUpOlP3eyvSJhe1XvvdViULGv2k9hmqVEDUsipwgu5OvFZgwVTbJLo/THas17lAMfCYU2VTNH98ZL1CsGSjvO3MHG9D8P2JvggU21OlxGoNMQe779qtXKa/CseVqcmKkDB6Z2jGmsu7NvrfGHIG/nByhluXKZM+6Lq/8TcLKUZxUzFlSFxgiQ9+sV4Gnv7catmWPAft82Uq9FIKs4qBixg1LeOvHFgiRb6v1bX7HMKQ1xX1ewIZa5BME4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEAaOwUoeLm9RQmi1qUtlN8KgIgQgVnTsQ1A7Q1mTaqyFtrZMNZxGWHw+9NPDfIJM/ESEyxk=\",\n            \"cardHolderName\": \"gazar visa\"\n        }\n    },\n    \"nickname\": \"blah vlah\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"RECIPIENT\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"category\": \"news\",\n    \"isFavorite\": true,\n    \"note\":\"Test\",\n    \"otp\":\"123311\"\n}"}}}, {"name": "Create a new beneficiary & pay", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries/pay", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"recipient\": {\n        \"type\": \"INSTANT_PAYMENT_ADDRESS\",\n        \"instantPaymentAddressRecipient\": {\n            \"instantPaymentAddress\": \"dinaebc123@demopay\"\n        }\n    },\n    \"nickname\": \"Donia 23\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"RECIPIENT\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"isNewCategory\": true,\n    \"category\": \"new category\",\n    \"isFavorite\": false,\n    \"note\": \"Test\",\n    \"otp\": \"123311\"\n}"}}}, {"name": "Get a certain beneficiary by beneficiary ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries/:globalId", "query": []}}}, {"name": "Get All Beneficiaries", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries?limit=100&minAmount&maxAmount&fromDate&toDate", "query": [{"key": "pointer", "value": "MjAyNS0wMy0wNVQxNzoxMToyNC44Mzla", "disabled": true}, {"key": "limit", "value": "100", "disabled": false}, {"key": "sortOrder", "value": "asc", "disabled": true}, {"key": "isFavorite", "value": "true", "disabled": true}, {"key": "minAmount", "value": "", "disabled": false}, {"key": "maxAmount", "value": "", "disabled": false}, {"key": "fromDate", "value": "", "disabled": false}, {"key": "toDate", "value": "", "disabled": false}, {"key": "nickname", "value": "mohamed", "disabled": true}, {"key": "categoryId", "value": "ee7d9363-6aca-4557-bfb9-2cea7735feeb", "disabled": true}]}}}, {"name": "Change favourite", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries/:globalId", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"isFavorite\": false\n}"}}}, {"name": "Delete a certain beneficiary by beneficiary ID", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries/:globalId", "query": []}}}]}, {"name": "Categories", "item": [{"name": "Create a new category", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/categories", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"categoryName\":\"testCat\"\n}"}}}, {"name": "Get all categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/categories", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \n}"}}}, {"name": "Get a certain category by category ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/categories/:globalId", "query": []}}}, {"name": "Update a certain category by category ID", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/categories/:globalId", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"categoryName\":\"updatedCat\"\n}"}}}, {"name": "Delete a certain category by category ID", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/categories/:globalId", "query": []}}}]}, {"name": "Others", "item": [{"name": "IPA Inquiry", "request": {"method": "POST", "header": [{"key": "Accept-Language", "value": "ar-EG", "disabled": false}], "url": {"raw": "{{base_url}}/v1/thub/ipn/request-name", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"identifier\":\"daainaebc123\",\n    \"type\": \"MOBILE_NUMBER\"\n}"}}}, {"name": "Get Banks List", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "ar-EG", "disabled": false}], "url": {"raw": "{{base_url}}/v1/thub/ipn/banks", "query": []}}}, {"name": "Get Transfer Reasons", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "ar-EG", "disabled": false}], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfer-reasons", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"cif\":\"121221\",\n    \"recipient\":{\n        \"type\": \"CARD\",\n    \"cardRecipient\": {\n        \"cardNumber\": \"****************\",\n        \"cardHolderName\": \"<PERSON><PERSON>\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON> 23\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"RECIPIENT\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"isNewCategory\":true,\n    \"category\": \"new category\",\n    \"isFavorite\": false,\n    \"note\":\"Test\",\n    \"otp\":\"123311\"\n}"}}}, {"name": "Decrypt message", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/decrypt-message", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "MIICEQYJKoZIhvcNAQcDoIICAjCCAf4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQBZJKnEW98d3Q1rLErruMi58CeWhZqHot2m/4HCJ0gq+gF3NuDG2qi2IDNqEMXDShqK4aOWn96OtCFCK6n00iL/orGAIrNxErz/iRE1zARpWcfxwG3N/z30BAJORbKS7cuG0raN3tqup3XpLECGiRVQq/6ZseF/s3QDQ3SfxJkLWDIq63ETR4Rzdt5vmd/h3pOhjYtA8+mjNbAUor//1quOcGUxpAodtcH2t9BnO+vN9pez5DWc84htY7DT4HI44o9j82qPiS9w4fvwxFWd2Av5RiWqGZTJilpZr5QZ3rk3371VLuaobcy2So2nCpdczkopI0a+KHghmmsuU6T2+cqVME4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEEgR5XDLFexpa8mlk73w14KgIgQgOOXimPIK02S/j2YMEV5UA9qNHsuL28DNvdje6A3GUAY=\n"}}}, {"name": "Get dummy message encrypted", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/encrypted-message", "query": []}}}]}, {"name": "Limits", "item": [{"name": "Usage", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/limits/usage", "query": []}}}, {"name": "checkLimits", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/limits/check", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"amount\":1000,\n    \"transferType\":\"IPN\"\n}"}}}, {"name": "addTransfer", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/limits/add", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"amount\":1000,\n    \"transferType\":\"IPN\"\n}"}}}, {"name": "rollbackTransfer", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v1/thub/limits/rollback", "query": []}, "body": {"mode": "raw", "disabled": false, "raw": "{\n    \"amount\":1000,\n    \"transferType\":\"IPN\",\n    \"date\":\"Wed+Feb+05+2025+15%3A12%3A20+GMT%2B0200+%28Eastern+European+Standard+Time%29\"\n}"}}}]}]}]}