<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Postman Converter - Comprehensive Solution Document</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        h4 {
            color: #34495e;
            margin-top: 20px;
        }
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc h2 {
            margin-top: 0;
            border-bottom: none;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        .architecture-diagram {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            font-family: monospace;
            text-align: center;
        }
        .feature-box {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .info-box {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .page-break {
            page-break-before: always;
        }
        @media print {
            body {
                font-size: 12pt;
            }
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>
    <h1>Postman Converter - Comprehensive Solution Document</h1>
    
    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#project-overview">1. Project Overview</a></li>
            <li><a href="#architecture">2. Architecture</a></li>
            <li><a href="#frontend-features">3. Frontend Plugin Features</a></li>
            <li><a href="#backend-functionality">4. Backend Plugin Functionality</a></li>
            <li><a href="#installation">5. Installation and Setup</a></li>
            <li><a href="#user-guide">6. User Guide</a></li>
            <li><a href="#technical-implementation">7. Technical Implementation</a></li>
            <li><a href="#api-documentation">8. API Documentation</a></li>
            <li><a href="#database-schema">9. Database Schema</a></li>
            <li><a href="#configuration">10. Configuration</a></li>
            <li><a href="#development-guide">11. Development Guide</a></li>
            <li><a href="#testing">12. Testing</a></li>
            <li><a href="#troubleshooting">13. Troubleshooting</a></li>
            <li><a href="#best-practices">14. Best Practices</a></li>
        </ul>
    </div>

    <div class="page-break"></div>
    
    <h2 id="project-overview">Project Overview</h2>
    
    <p>The Postman Converter is a comprehensive Backstage plugin solution that provides powerful tools for API development, testing, and automation. It consists of two main components:</p>
    
    <ul>
        <li><strong>Frontend Plugin</strong> (<code>@internal/plugin-postman-converter</code>): A React-based user interface with three main features</li>
        <li><strong>Backend Plugin</strong> (<code>@internal/plugin-postman-converter-backend</code>): A Node.js/Express API server with database integration</li>
    </ul>

    <div class="feature-box">
        <h3>Key Features</h3>
        <ul>
            <li><strong>Collection Management</strong>: Full CRUD operations for Postman collections with version history</li>
            <li><strong>K6 Converter</strong>: Convert Postman collections to K6 performance testing scripts</li>
            <li><strong>API Testing Tool</strong>: Comprehensive API testing environment with request execution and test generation</li>
            <li><strong>Role-based Access Control</strong>: User and admin-level permissions</li>
            <li><strong>Version History</strong>: Complete audit trail for collection changes</li>
            <li><strong>Modern UI</strong>: Material-UI based responsive design</li>
        </ul>
    </div>

    <div class="page-break"></div>
    
    <h2 id="architecture">Architecture</h2>

    <h3>System Architecture</h3>
    
    <div class="architecture-diagram">
        <pre>
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │    Database     │
│   (React)       │◄──►│   (Express)     │◄──►│   (SQLite/PG)   │
│                 │    │                 │    │                 │
│ • Collection    │    │ • REST API      │    │ • Collections   │
│   Management    │    │ • Auth/Auth     │    │ • Versions      │
│ • K6 Converter  │    │ • CRUD Ops      │    │ • History       │
│ • API Testing   │    │ • Validation    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        </pre>
    </div>

    <h3>Technology Stack</h3>
    
    <div class="info-box">
        <h4>Frontend:</h4>
        <ul>
            <li>React 18+ with TypeScript</li>
            <li>Material-UI v5 for components</li>
            <li>React Router for navigation</li>
            <li>Backstage Core APIs for integration</li>
        </ul>
    </div>

    <div class="info-box">
        <h4>Backend:</h4>
        <ul>
            <li>Node.js with Express framework</li>
            <li>Knex.js for database operations</li>
            <li>Zod for schema validation</li>
            <li>Winston for logging</li>
            <li>UUID for unique identifiers</li>
        </ul>
    </div>

    <div class="info-box">
        <h4>Database:</h4>
        <ul>
            <li>SQLite (development)</li>
            <li>PostgreSQL (production)</li>
            <li>Automatic table creation and migration</li>
        </ul>
    </div>

    <div class="page-break"></div>
    
    <h2 id="frontend-features">Frontend Plugin Features</h2>

    <h3>1. Collection Management</h3>
    
    <p>The Collection Management feature provides a comprehensive interface for managing Postman collections:</p>

    <h4>Key Components:</h4>
    <ul>
        <li><strong>Collection List Page</strong>: Displays all collections in a sortable table</li>
        <li><strong>Collection Detail Page</strong>: Shows collection metadata and version history</li>
        <li><strong>Collection Form</strong>: Create and edit collection forms with validation</li>
        <li><strong>Tree View</strong>: Hierarchical display of collection structure</li>
    </ul>

    <h4>Features:</h4>
    <ul>
        <li>Create, read, update, and delete collections</li>
        <li>Upload Postman collection JSON files</li>
        <li>View collection metadata (name, description, owner, dates)</li>
        <li>Access version history and rollback capabilities</li>
        <li>Role-based access control (users see own collections, admins see all)</li>
    </ul>

    <h3>2. K6 Converter</h3>
    
    <p>The K6 Converter transforms Postman collections into K6 performance testing scripts:</p>

    <h4>Key Components:</h4>
    <ul>
        <li><strong>Collection Selector</strong>: Choose from stored collections</li>
        <li><strong>Folder Selector</strong>: Select specific folders or requests</li>
        <li><strong>Configuration Panel</strong>: Customize conversion settings</li>
        <li><strong>Script Output Panel</strong>: View and copy generated scripts</li>
    </ul>

    <h4>Features:</h4>
    <ul>
        <li>Convert entire collections or selected folders</li>
        <li>Generate separate files for actions, scenarios, and load configuration</li>
        <li>Configurable virtual users (VUs) and test duration</li>
        <li>Support for HTTP methods, headers, and request bodies</li>
        <li>Automatic test assertion generation</li>
        <li>Split or combined file output formats</li>
    </ul>

    <h3>3. API Testing Tool</h3>
    
    <p>The API Testing Tool provides a Postman-like environment for API testing:</p>

    <h4>Key Components:</h4>
    <ul>
        <li><strong>Collections Sidebar</strong>: Hierarchical view of collections and requests</li>
        <li><strong>Request Panel</strong>: Configure HTTP requests with method, URL, headers, and body</li>
        <li><strong>Response Panel</strong>: View response data, headers, and status</li>
        <li><strong>Test Panel</strong>: Generate and execute test scripts</li>
        <li><strong>Environment Panel</strong>: Manage environment variables</li>
    </ul>

    <h4>Features:</h4>
    <ul>
        <li>Send HTTP requests with full configuration options</li>
        <li>Automatic test script generation based on responses</li>
        <li>Test execution with Postman-compatible syntax</li>
        <li>Environment variable support with substitution</li>
        <li>Request/response history</li>
        <li>Collection import and export</li>
        <li>Resizable panels for optimal workspace layout</li>
    </ul>

    <div class="page-break"></div>
    
    <h2 id="backend-functionality">Backend Plugin Functionality</h2>

    <h3>Core Services</h3>
    
    <p>The backend plugin provides a RESTful API with the following services:</p>

    <h4>Authentication & Authorization</h4>
    <ul>
        <li>Integration with Backstage authentication system</li>
        <li>Role-based access control (user/admin)</li>
        <li>Fallback to guest user for unauthenticated requests</li>
        <li>JWT token validation and user identification</li>
    </ul>

    <h4>Database Operations</h4>
    <ul>
        <li>Automatic table creation and schema management</li>
        <li>Transaction support for data consistency</li>
        <li>Connection pooling and query optimization</li>
        <li>Support for SQLite (development) and PostgreSQL (production)</li>
    </ul>

    <h4>Validation & Error Handling</h4>
    <ul>
        <li>Zod schema validation for all inputs</li>
        <li>Comprehensive error handling with appropriate HTTP status codes</li>
        <li>Request/response logging for debugging</li>
        <li>Input sanitization and security measures</li>
    </ul>

    <div class="page-break"></div>
    
    <h2 id="installation">Installation and Setup</h2>

    <h3>Prerequisites</h3>
    <ul>
        <li>Node.js 20 or 22</li>
        <li>Yarn package manager</li>
        <li>Backstage application (v1.0+)</li>
        <li>Database (SQLite for development, PostgreSQL for production)</li>
    </ul>

    <h3>Backend Plugin Installation</h3>
    
    <div class="warning-box">
        <strong>Step 1:</strong> Install the backend plugin package:
        <pre><code># From your root directory
yarn --cwd packages/backend add @internal/plugin-postman-converter-backend</code></pre>
    </div>

    <div class="warning-box">
        <strong>Step 2:</strong> Add the plugin to your backend:
        <pre><code>// packages/backend/src/index.ts
const backend = createBackend();
// ... other plugins
backend.add(import('@internal/plugin-postman-converter-backend'));</code></pre>
    </div>

    <div class="warning-box">
        <strong>Step 3:</strong> Configure database (optional):
        <pre><code># app-config.yaml
backend:
  database:
    client: pg
    connection:
      host: ${POSTGRES_HOST}
      port: ${POSTGRES_PORT}
      user: ${POSTGRES_USER}
      password: ${POSTGRES_PASSWORD}
      database: ${POSTGRES_DB}</code></pre>
    </div>

    <h3>Frontend Plugin Installation</h3>
    
    <div class="warning-box">
        <strong>Step 1:</strong> Install the frontend plugin package:
        <pre><code># From your root directory
yarn --cwd packages/app add @internal/plugin-postman-converter</code></pre>
    </div>

    <div class="warning-box">
        <strong>Step 2:</strong> Add routes to your app:
        <pre><code>// packages/app/src/App.tsx
import { PostmanConverterPage } from '@internal/plugin-postman-converter';

const routes = (
  &lt;FlatRoutes&gt;
    {/* ... other routes */}
    &lt;Route path="/postman-converter" element={&lt;PostmanConverterPage /&gt;} /&gt;
  &lt;/FlatRoutes&gt;
);</code></pre>
    </div>

    <div class="warning-box">
        <strong>Step 3:</strong> Add navigation (optional):
        <pre><code>// packages/app/src/components/Root/Root.tsx
import PostmanIcon from '@mui/icons-material/Api';

&lt;SidebarItem icon={PostmanIcon} to="postman-converter" text="Postman Converter" /&gt;</code></pre>
    </div>

    <h3>Development Setup</h3>
    
    <div class="info-box">
        <strong>Clone and install dependencies:</strong>
        <pre><code>git clone &lt;repository-url&gt;
cd backstage-app
yarn install</code></pre>
    </div>

    <div class="info-box">
        <strong>Start development servers:</strong>
        <pre><code># Start backend (terminal 1)
yarn --cwd packages/backend start

# Start frontend (terminal 2)
yarn start</code></pre>
    </div>

    <div class="info-box">
        <strong>Access the application:</strong>
        <ul>
            <li>Frontend: <a href="http://localhost:3000">http://localhost:3000</a></li>
            <li>Backend API: <a href="http://localhost:7007/api/postman-converter">http://localhost:7007/api/postman-converter</a></li>
        </ul>
    </div>

    <div class="page-break"></div>
    
    <h2 id="user-guide">User Guide</h2>

    <h3>Getting Started</h3>
    
    <ol>
        <li><strong>Access the Plugin:</strong> Navigate to <code>/postman-converter</code> in your Backstage application</li>
        <li><strong>Choose a Feature:</strong>
            <ul>
                <li><strong>Collections</strong>: Manage your Postman collections</li>
                <li><strong>K6 Converter</strong>: Convert collections to K6 scripts</li>
                <li><strong>API Testing</strong>: Test APIs interactively</li>
            </ul>
        </li>
    </ol>

    <h3>Collection Management Workflow</h3>
    
    <ol>
        <li><strong>Create a Collection:</strong>
            <ul>
                <li>Click "Add Collection" button</li>
                <li>Fill in name and description</li>
                <li>Upload Postman collection JSON file</li>
                <li>Click "Save" to create</li>
            </ul>
        </li>
        <li><strong>View Collections:</strong>
            <ul>
                <li>Browse collections in the table view</li>
                <li>Click on a collection to view details</li>
                <li>Access version history and metadata</li>
            </ul>
        </li>
        <li><strong>Edit Collections:</strong>
            <ul>
                <li>Click "Edit" on any collection</li>
                <li>Modify name, description, or content</li>
                <li>Changes create new versions automatically</li>
            </ul>
        </li>
        <li><strong>Delete Collections:</strong>
            <ul>
                <li>Click "Delete" on any collection</li>
                <li>Confirm deletion (irreversible)</li>
            </ul>
        </li>
    </ol>

    <h3>K6 Converter Workflow</h3>
    
    <ol>
        <li><strong>Select Collection:</strong>
            <ul>
                <li>Choose from dropdown of available collections</li>
                <li>Collection structure loads automatically</li>
            </ul>
        </li>
        <li><strong>Configure Conversion:</strong>
            <ul>
                <li>Select folders/requests to include</li>
                <li>Set virtual users (VUs) and duration</li>
                <li>Choose output format (split/combined)</li>
                <li>Enable/disable checks and sleep statements</li>
            </ul>
        </li>
        <li><strong>Generate Scripts:</strong>
            <ul>
                <li>Click "Generate K6 Script"</li>
                <li>View generated files in tabs</li>
                <li>Copy scripts to clipboard or download</li>
            </ul>
        </li>
    </ol>

    <h3>API Testing Workflow</h3>
    
    <ol>
        <li><strong>Create/Import Collection:</strong>
            <ul>
                <li>Add new collection or import existing</li>
                <li>Organize requests in folders</li>
            </ul>
        </li>
        <li><strong>Configure Request:</strong>
            <ul>
                <li>Set HTTP method and URL</li>
                <li>Add headers and request body</li>
                <li>Configure URL parameters</li>
            </ul>
        </li>
        <li><strong>Send Request:</strong>
            <ul>
                <li>Click "Send" to execute request</li>
                <li>View response data and headers</li>
                <li>Check response time and status</li>
            </ul>
        </li>
        <li><strong>Generate Tests:</strong>
            <ul>
                <li>Auto-generate test scripts from response</li>
                <li>Customize test assertions</li>
                <li>Run tests to validate responses</li>
            </ul>
        </li>
        <li><strong>Save Work:</strong>
            <ul>
                <li>Save individual collections</li>
                <li>Use "Save All" for bulk operations</li>
                <li>Export collections for sharing</li>
            </ul>
        </li>
    </ol>

    <div class="page-break"></div>
    
    <p><strong>Document Version:</strong> 1.0<br>
    <strong>Last Updated:</strong> January 2024<br>
    <strong>Plugin Version:</strong> 0.1.0</p>

    <div class="info-box">
        <h3>Converting to Microsoft Word</h3>
        <p>To convert this HTML document to Microsoft Word format (.docx):</p>
        <ol>
            <li><strong>Option 1:</strong> Open this HTML file in Microsoft Word and save as .docx</li>
            <li><strong>Option 2:</strong> Use an online HTML to Word converter</li>
            <li><strong>Option 3:</strong> Use pandoc command: <code>pandoc Postman_Converter_Solution_Document.html -o Postman_Converter_Solution_Document.docx</code></li>
        </ol>
        <p>The complete technical documentation continues in the Markdown file: <code>Postman_Converter_Solution_Document.md</code></p>
    </div>

</body>
</html>
