# Features Plugin

A complete CRUD (Create, Read, Update, Delete) plugin for managing organizational features in Backstage, with a many-to-one relationship to products.

## Overview

The Features plugin allows organizations to:
- Manage features with full CRUD operations
- Assign features to products (many-to-one relationship)
- Search and filter features
- Track feature status (active, inactive, deprecated)
- Maintain referential integrity with products

## Features

### ✅ Complete CRUD Operations
- **CREATE**: Add new features and assign them to products
- **READ**: View all features, individual feature details, and product information
- **UPDATE**: Modify feature details and reassign to different products
- **DELETE**: Remove features with confirmation dialogs

### 🏢 Product Integration
- Many-to-one relationship (each feature belongs to exactly one product)
- Foreign key constraints ensure referential integrity
- Features can only be assigned to valid products
- Cascade delete when products are removed

### 🔍 Advanced Features
- **Search**: Find features by name or description
- **Filter**: View features by department
- **Status Management**: Track feature lifecycle (active, inactive, deprecated)
- **Validation**: Unique feature names within the same department
- **Error Handling**: Comprehensive validation and user-friendly error messages

### 🎨 User Interface
- Beautiful Material-UI design matching Backstage aesthetics
- Responsive grid layout with hover effects and animations
- Form validation with real-time feedback
- Loading states and error handling
- Confirmation dialogs for destructive actions

## Installation

The plugin is already integrated into this Backstage application. To use it in a new Backstage app:

1. Add the plugin packages to your dependencies
2. Install the backend plugin in your backend
3. Install the frontend plugin in your app
4. Add the route to your app's routing

## Usage

### Accessing the Plugin
- Navigate to `/features` in your Backstage application
- Use the sidebar navigation "Features" link

### Creating Features
1. Click "Add Feature" button
2. Fill in the form:
   - **Name**: Unique name within the product
   - **Description**: Optional detailed description
   - **Product**: Select from existing products
   - **Status**: Choose active, inactive, or deprecated
3. Click "Create Feature"

### Managing Features
- **View**: Click on any feature card to see details
- **Edit**: Click "Edit" button on feature detail page
- **Delete**: Click "Delete" button with confirmation
- **Search**: Use the search box to find features
- **Filter**: Features automatically show product information

## API Endpoints

### Backend API (`/api/features`)

- `GET /features` - List all features with product information
- `GET /features?search=query` - Search features by name/description
- `GET /features?product_id=id` - Filter features by product
- `GET /features/:id` - Get specific feature with product details
- `POST /features` - Create new feature
- `PUT /features/:id` - Update existing feature
- `DELETE /features/:id` - Delete feature

### Request/Response Examples

#### Create Feature
```json
POST /api/features/features
{
  "name": "User Authentication",
  "description": "Secure user login and registration system",
  "departmentId": "dept-123",
  "status": "active"
}
```

#### Response
```json
{
  "data": {
    "id": "feat-456",
    "name": "User Authentication",
    "description": "Secure user login and registration system",
    "departmentId": "dept-123",
    "status": "active",
    "createdAt": "2025-06-14T13:30:00.000Z",
    "updatedAt": "2025-06-14T13:30:00.000Z",
    "department": {
      "id": "dept-123",
      "name": "Engineering",
      "description": "Software development and technical operations"
    }
  },
  "message": "Feature created successfully"
}
```

## Database Schema

### Features Table
```sql
CREATE TABLE features (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  product_id TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
  UNIQUE (name, product_id)
);
```

### Indexes
- `name` - For search performance
- `product_id` - For filtering by product
- `status` - For status-based queries
- `created_at` - For chronological sorting

## Business Rules

1. **Unique Names**: Feature names must be unique within the same product
2. **Valid Products**: Features can only be assigned to existing products
3. **Referential Integrity**: Deleting a product removes all its features
4. **Status Validation**: Status must be one of: active, inactive, deprecated
5. **Name Length**: Feature names limited to 100 characters
6. **Description Length**: Descriptions limited to 500 characters

## Error Handling

The plugin provides comprehensive error handling:
- **Validation Errors**: Form validation with field-specific messages
- **Business Logic Errors**: Clear messages for rule violations
- **Network Errors**: User-friendly error displays
- **Not Found Errors**: Proper 404 handling
- **Server Errors**: Graceful degradation with error boundaries

## Development

### Running Tests
```bash
# Backend tests
cd plugins/features-backend
yarn test

# Frontend tests
cd plugins/features
yarn test
```

### Development Server
```bash
# Start backend development server
cd plugins/features-backend
yarn start

# Start frontend development server
cd plugins/features
yarn start
```

## Architecture

### Backend Architecture
- **FeatureStore**: Database operations and business logic
- **Router**: API endpoints and request handling
- **Migrations**: Database schema management
- **Plugin**: Backstage plugin integration

### Frontend Architecture
- **API Client**: Type-safe API communication
- **Components**: Reusable UI components
- **Pages**: Route-specific page components
- **Router**: Client-side routing

### Integration Points
- **Authentication**: Uses Backstage auth system
- **Database**: Integrates with Backstage database
- **Logging**: Uses Backstage logging infrastructure
- **Configuration**: Follows Backstage config patterns

## Contributing

1. Follow Backstage development guidelines
2. Maintain test coverage
3. Update documentation for new features
4. Follow the existing code style and patterns

## License

Apache-2.0 License - same as Backstage
