import React from 'react';
import { Routes, Route } from 'react-router-dom';

import { FeaturesListPage } from '../FeaturesListPage';
import { FeatureDetailPage } from '../FeatureDetailPage';
import { FeatureNewPage } from '../FeatureNewPage';
import { FeatureEditPage } from '../FeatureEditPage';

export const FeaturesRouter = () => {
  return (
    <Routes>
      <Route path="/" element={<FeaturesListPage />} />
      <Route path="/new" element={<FeatureNewPage />} />
      <Route path="/:id" element={<FeatureDetailPage />} />
      <Route path="/:id/edit" element={<FeatureEditPage />} />
    </Routes>
  );
};
