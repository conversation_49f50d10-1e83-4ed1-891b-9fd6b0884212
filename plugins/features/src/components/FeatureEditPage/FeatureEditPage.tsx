import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Page,
  Header,
  Content,
  Progress,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';
import { Box, IconButton, Tooltip } from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';

import { featuresApiRef, UpdateFeatureInput, Feature } from '../../api';
import { FeatureForm } from '../FeatureForm';

export const FeatureEditPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const featuresApi = useApi(featuresApiRef);
  
  const [feature, setFeature] = useState<Feature | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadFeature = async () => {
      if (!id) {
        setError('Feature ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const result = await featuresApi.getFeatureById(id);
        setFeature(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load feature');
      } finally {
        setLoading(false);
      }
    };

    loadFeature();
  }, [id, featuresApi]);

  const handleSubmit = async (data: UpdateFeatureInput) => {
    if (!feature) return;

    try {
      setSubmitting(true);
      setError(null);
      const updatedFeature = await featuresApi.updateFeature(feature.id, data);
      navigate(`/features/${updatedFeature.id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update feature');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (feature) {
      navigate(`/features/${feature.id}`);
    } else {
      navigate('/features');
    }
  };

  if (loading) {
    return (
      <Page themeId="tool">
        <Header title="Edit Feature" />
        <Content>
          <Progress />
        </Content>
      </Page>
    );
  }

  if (error || !feature) {
    return (
      <Page themeId="tool">
        <Header title="Edit Feature" />
        <Content>
          <ErrorPanel error={new Error(error || 'Feature not found')} />
        </Content>
      </Page>
    );
  }

  return (
    <Page themeId="tool">
      <Header title={`Edit ${feature.name}`} subtitle="Update feature information">
        <Tooltip title="Back to Feature">
          <IconButton onClick={handleCancel} color="inherit">
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
      </Header>
      <Content>
        <Box sx={{ maxWidth: 800, mx: 'auto', py: 4 }}>
          <FeatureForm
            feature={feature}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={submitting}
            error={error}
          />
        </Box>
      </Content>
    </Page>
  );
};
