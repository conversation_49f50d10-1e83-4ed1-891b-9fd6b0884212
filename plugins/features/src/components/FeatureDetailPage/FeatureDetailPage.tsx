import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Page,
  Header,
  Content,
  Progress,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Typography,
  Grid,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  Extension as FeatureIcon,
  Business as ProductIcon,
  CalendarToday as CalendarIcon,
  Update as UpdateIcon,
} from '@mui/icons-material';

import { featuresApiRef, Feature } from '../../api';
import { DeleteConfirmationDialog } from '../DeleteConfirmationDialog';

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'warning';
    case 'deprecated':
      return 'error';
    default:
      return 'default';
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'active':
      return 'Active';
    case 'inactive':
      return 'Inactive';
    case 'deprecated':
      return 'Deprecated';
    default:
      return status;
  }
};

export const FeatureDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const featuresApi = useApi(featuresApiRef);
  
  const [feature, setFeature] = useState<Feature | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    const loadFeature = async () => {
      if (!id) {
        setError('Feature ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const result = await featuresApi.getFeatureById(id);
        setFeature(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load feature');
      } finally {
        setLoading(false);
      }
    };

    loadFeature();
  }, [id, featuresApi]);

  const handleDelete = async () => {
    if (!feature) return;

    try {
      setDeleting(true);
      await featuresApi.deleteFeature(feature.id);
      navigate('/features');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete feature');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <Page themeId="tool">
        <Header title="Feature Details" />
        <Content>
          <Progress />
        </Content>
      </Page>
    );
  }

  if (error || !feature) {
    return (
      <Page themeId="tool">
        <Header title="Feature Details" />
        <Content>
          <ErrorPanel error={new Error(error || 'Feature not found')} />
        </Content>
      </Page>
    );
  }

  return (
    <Page themeId="tool">
      <Header title={feature.name} subtitle="Feature Details">
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Back to Features">
            <IconButton onClick={() => navigate('/features')} color="inherit">
              <ArrowBackIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => navigate(`/features/${feature.id}/edit`)}
            sx={{ color: 'inherit', borderColor: 'inherit' }}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            startIcon={<DeleteIcon />}
            onClick={() => setDeleteDialogOpen(true)}
            sx={{ color: 'inherit', borderColor: 'inherit' }}
          >
            Delete
          </Button>
        </Box>
      </Header>
      <Content>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card sx={{ mb: 3 }}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <FeatureIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4" component="h1" fontWeight={600}>
                      {feature.name}
                    </Typography>
                    <Chip
                      label={getStatusLabel(feature.status)}
                      color={getStatusColor(feature.status) as any}
                      sx={{ mt: 1, fontWeight: 500 }}
                    />
                  </Box>
                </Box>

                {feature.description && (
                  <>
                    <Divider sx={{ my: 3 }} />
                    <Box>
                      <Typography variant="h6" gutterBottom fontWeight={600}>
                        Description
                      </Typography>
                      <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                        {feature.description}
                      </Typography>
                    </Box>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Details
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {feature.product && (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <ProductIcon sx={{ fontSize: 20, color: 'text.secondary', mr: 2 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Product
                        </Typography>
                        <Typography variant="body1" fontWeight={500}>
                          {feature.product.name}
                        </Typography>
                        {feature.product.description && (
                          <Typography variant="caption" color="text.secondary">
                            {feature.product.description}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  )}

                  <Divider />

                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CalendarIcon sx={{ fontSize: 20, color: 'text.secondary', mr: 2 }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Created
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {new Date(feature.createdAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(feature.createdAt).toLocaleTimeString()}
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <UpdateIcon sx={{ fontSize: 20, color: 'text.secondary', mr: 2 }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Last Updated
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {new Date(feature.updatedAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(feature.updatedAt).toLocaleTimeString()}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          onConfirm={handleDelete}
          loading={deleting}
          itemName={feature.name}
          itemType="feature"
        />
      </Content>
    </Page>
  );
};
