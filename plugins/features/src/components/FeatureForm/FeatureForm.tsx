import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useApi } from '@backstage/core-plugin-api';
import { productApiRef } from '@internal/plugin-product';

import { CreateFeatureInput, UpdateFeatureInput, Feature } from '../../api';

interface Product {
  id: string;
  name: string;
  description?: string;
}

interface FeatureFormProps {
  feature?: Feature;
  onSubmit: (data: CreateFeatureInput | UpdateFeatureInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

export const FeatureForm: React.FC<FeatureFormProps> = ({
  feature,
  onSubmit,
  onCancel,
  loading = false,
  error = null,
}) => {
  const productApi = useApi(productApiRef);

  const [formData, setFormData] = useState({
    name: feature?.name || '',
    description: feature?.description || '',
    productId: feature?.productId || '',
    status: feature?.status || 'active' as const,
  });

  const [products, setProducts] = useState<Product[]>([]);
  const [productsLoading, setProductsLoading] = useState(true);
  const [productsError, setProductsError] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setProductsLoading(true);
        setProductsError(null);
        const result = await productApi.getProducts();
        setProducts(result);
      } catch (err) {
        setProductsError(err instanceof Error ? err.message : 'Failed to load products');
      } finally {
        setProductsLoading(false);
      }
    };

    loadProducts();
  }, [productApi]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    } else if (formData.name.length > 100) {
      errors.name = 'Name must be 100 characters or less';
    }

    if (formData.description && formData.description.length > 500) {
      errors.description = 'Description must be 500 characters or less';
    }

    if (!formData.productId) {
      errors.productId = 'Product is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (err) {
      // Error handling is done by parent component
    }
  };

  const handleInputChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | { target: { value: unknown } }
  ) => {
    const value = event.target.value as string;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const isFormValid = formData.name.trim() && formData.productId && Object.keys(formErrors).length === 0;

  return (
    <Card sx={{ maxWidth: 600, mx: 'auto' }}>
      <CardContent sx={{ p: 4 }}>
        <Typography variant="h5" component="h2" gutterBottom fontWeight={600}>
          {feature ? 'Edit Feature' : 'Create New Feature'}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          {feature 
            ? 'Update the feature information below.'
            : 'Fill in the details to create a new feature.'
          }
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {productsError && (
          <Alert severity="error" sx={{ mb: 3 }}>
            Failed to load products: {productsError}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <TextField
            label="Name"
            value={formData.name}
            onChange={handleInputChange('name')}
            error={!!formErrors.name}
            helperText={formErrors.name || 'Enter a descriptive name for the feature'}
            required
            fullWidth
            disabled={loading}
            inputProps={{ maxLength: 100 }}
          />

          <TextField
            label="Description"
            value={formData.description}
            onChange={handleInputChange('description')}
            error={!!formErrors.description}
            helperText={formErrors.description || 'Optional description of the feature'}
            multiline
            rows={3}
            fullWidth
            disabled={loading}
            inputProps={{ maxLength: 500 }}
          />

          <FormControl fullWidth required error={!!formErrors.productId}>
            <InputLabel>Product</InputLabel>
            <Select
              value={formData.productId}
              onChange={handleInputChange('productId')}
              label="Product"
              disabled={loading || productsLoading}
            >
              {productsLoading ? (
                <MenuItem disabled>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Loading products...
                </MenuItem>
              ) : products.length === 0 ? (
                <MenuItem disabled>No products available</MenuItem>
              ) : (
                products.map((product) => (
                  <MenuItem key={product.id} value={product.id}>
                    <Box>
                      <Typography variant="body1">{product.name}</Typography>
                      {product.description && (
                        <Typography variant="caption" color="text.secondary">
                          {product.description}
                        </Typography>
                      )}
                    </Box>
                  </MenuItem>
                ))
              )}
            </Select>
            {formErrors.productId && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                {formErrors.productId}
              </Typography>
            )}
          </FormControl>

          <FormControl fullWidth>
            <InputLabel>Status</InputLabel>
            <Select
              value={formData.status}
              onChange={handleInputChange('status')}
              label="Status"
              disabled={loading}
            >
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
              <MenuItem value="deprecated">Deprecated</MenuItem>
            </Select>
          </FormControl>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
            <Button
              variant="outlined"
              onClick={onCancel}
              disabled={loading}
              sx={{ minWidth: 100 }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={loading || !isFormValid || productsLoading}
              sx={{ minWidth: 100 }}
            >
              {loading ? (
                <CircularProgress size={20} />
              ) : (
                feature ? 'Update Feature' : 'Create Feature'
              )}
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};
