import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Page,
  Header,
  Content,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';
import { Box, IconButton, Tooltip } from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';

import { featuresApiRef, CreateFeatureInput } from '../../api';
import { FeatureForm } from '../FeatureForm';

export const FeatureNewPage = () => {
  const navigate = useNavigate();
  const featuresApi = useApi(featuresApiRef);
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (data: CreateFeatureInput) => {
    try {
      setLoading(true);
      setError(null);
      const feature = await featuresApi.createFeature(data);
      navigate(`/features/${feature.id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create feature');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/features');
  };

  return (
    <Page themeId="tool">
      <Header title="Create New Feature" subtitle="Add a new feature to your organization">
        <Tooltip title="Back to Features">
          <IconButton onClick={() => navigate('/features')} color="inherit">
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
      </Header>
      <Content>
        <Box sx={{ maxWidth: 800, mx: 'auto', py: 4 }}>
          <FeatureForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={loading}
            error={error}
          />
        </Box>
      </Content>
    </Page>
  );
};
