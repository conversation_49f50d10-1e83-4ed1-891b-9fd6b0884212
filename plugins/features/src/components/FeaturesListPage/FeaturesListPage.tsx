import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Page,
  Header,
  Content,
  ContentHeader,
  SupportButton,
  Progress,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  Avatar,
  Fade,
  ToggleButton,
  ToggleButtonGroup,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Extension as FeatureIcon,
  Business as ProductIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
} from '@mui/icons-material';

import { featuresApiRef, Feature } from '../../api';

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'warning';
    case 'deprecated':
      return 'error';
    default:
      return 'default';
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'active':
      return 'Active';
    case 'inactive':
      return 'Inactive';
    case 'deprecated':
      return 'Deprecated';
    default:
      return status;
  }
};

export const FeaturesListPage = () => {
  const navigate = useNavigate();
  const featuresApi = useApi(featuresApiRef);
  
  const [features, setFeatures] = useState<Feature[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const loadFeatures = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let result: Feature[];
      if (searchQuery.trim()) {
        result = await featuresApi.searchFeatures(searchQuery.trim());
      } else {
        result = await featuresApi.getFeatures();
      }
      
      setFeatures(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load features');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFeatures();
  }, [searchQuery]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  if (loading) {
    return (
      <Page themeId="tool">
        <Header title="Features">
          <SupportButton>Manage your organization's features</SupportButton>
        </Header>
        <Content>
          <Progress />
        </Content>
      </Page>
    );
  }

  if (error) {
    return (
      <Page themeId="tool">
        <Header title="Features">
          <SupportButton>Manage your organization's features</SupportButton>
        </Header>
        <Content>
          <ErrorPanel error={new Error(error)} />
        </Content>
      </Page>
    );
  }

  return (
    <Page themeId="tool">
      <Header title="Features" subtitle="Manage your organization's features">
        <SupportButton>Manage features in your organization</SupportButton>
      </Header>
      <Content>
        <ContentHeader title="All Features">
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/features/new')}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 600,
              px: 3,
              py: 1.5,
              boxShadow: 2,
              '&:hover': {
                boxShadow: 4,
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
            }}
          >
            Add Feature
          </Button>
        </ContentHeader>

        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              variant="outlined"
              placeholder="Search features..."
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                flexGrow: 1,
                maxWidth: 600,
                minWidth: 300,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'background.paper',
                },
              }}
            />

            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={(_, newView) => newView && setViewMode(newView)}
              size="small"
              sx={{
                '& .MuiToggleButton-root': {
                  borderRadius: 2,
                  px: 2,
                },
              }}
            >
              <ToggleButton value="grid" aria-label="grid view">
                <GridViewIcon />
              </ToggleButton>
              <ToggleButton value="list" aria-label="list view">
                <ListViewIcon />
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </Box>

        {features.length === 0 ? (
          <Fade in timeout={300}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: 400,
                textAlign: 'center',
                py: 8,
              }}
            >
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  bgcolor: 'primary.light',
                  mb: 3,
                }}
              >
                <FeatureIcon sx={{ fontSize: 60 }} />
              </Avatar>
              <Typography variant="h4" color="text.primary" gutterBottom fontWeight={600}>
                {searchQuery ? 'No features found' : 'No features yet'}
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 400 }}>
                {searchQuery
                  ? 'Try adjusting your search criteria or create a new feature'
                  : 'Get started by creating your first feature to organize your capabilities'}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                size="large"
                startIcon={<AddIcon />}
                onClick={() => navigate('/features/new')}
                sx={{
                  borderRadius: 3,
                  textTransform: 'none',
                  fontWeight: 600,
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  boxShadow: 3,
                  '&:hover': {
                    boxShadow: 6,
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.3s ease-in-out',
                }}
              >
                {searchQuery ? 'Create New Feature' : 'Add Your First Feature'}
              </Button>
            </Box>
          </Fade>
        ) : viewMode === 'grid' ? (
          <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mt: 0 }}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} xl={3} key={feature.id}>
                <Fade in timeout={300 + index * 100}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: 3,
                      boxShadow: 2,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        boxShadow: 8,
                        transform: 'translateY(-4px)',
                      },
                      cursor: 'pointer',
                      minHeight: 360, // Increased for better consistency
                    }}
                    onClick={() => navigate(`/features/${feature.id}`)}
                  >
                    <CardContent
                      sx={{
                        flexGrow: 1,
                        p: 3,
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between', // Better distribution
                      }}
                    >
                      {/* Header Section - Fixed height */}
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              bgcolor: 'primary.main',
                              mr: 2,
                              flexShrink: 0,
                            }}
                          >
                            <FeatureIcon />
                          </Avatar>
                          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                            <Typography
                              variant="h6"
                              component="h2"
                              sx={{
                                fontWeight: 600,
                                color: 'text.primary',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                lineHeight: 1.3,
                                minHeight: '2.6em', // Fixed height for 2 lines
                                mb: 1,
                              }}
                            >
                              {feature.name}
                            </Typography>
                            <Chip
                              label={getStatusLabel(feature.status)}
                              color={getStatusColor(feature.status) as any}
                              size="small"
                              sx={{ fontWeight: 500 }}
                            />
                          </Box>
                        </Box>

                        {/* Description Section - Flexible height */}
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 3,
                            WebkitBoxOrient: 'vertical',
                            lineHeight: 1.5,
                            minHeight: '4.5em', // Fixed height for 3 lines
                            mb: 2,
                          }}
                        >
                          {feature.description || 'No description available'}
                        </Typography>
                      </Box>

                      {/* Footer Section - Fixed at bottom */}
                      <Box sx={{ mt: 'auto' }}>
                        {feature.product && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <ProductIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                fontWeight: 500,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                flexGrow: 1,
                              }}
                            >
                              {feature.product.name}
                            </Typography>
                          </Box>
                        )}

                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{
                            fontSize: '0.75rem',
                            display: 'block',
                          }}
                        >
                          Created {new Date(feature.createdAt).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Fade in timeout={300}>
            <TableContainer component={Paper} sx={{ borderRadius: 3, boxShadow: 2 }}>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: 'grey.50' }}>
                    <TableCell sx={{ fontWeight: 600, py: 2 }}>Feature</TableCell>
                    <TableCell sx={{ fontWeight: 600, py: 2 }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 600, py: 2 }}>Product</TableCell>
                    <TableCell sx={{ fontWeight: 600, py: 2 }}>Description</TableCell>
                    <TableCell sx={{ fontWeight: 600, py: 2 }}>Created</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {features.map((feature) => (
                    <TableRow
                      key={feature.id}
                      sx={{
                        cursor: 'pointer',
                        transition: 'background-color 0.2s ease',
                        '&:hover': {
                          backgroundColor: 'action.hover',
                        },
                        '&:nth-of-type(even)': {
                          backgroundColor: 'action.selected',
                        },
                      }}
                      onClick={() => navigate(`/features/${feature.id}`)}
                    >
                      <TableCell sx={{ py: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            sx={{
                              width: 32,
                              height: 32,
                              bgcolor: 'primary.main',
                              mr: 2,
                              flexShrink: 0,
                            }}
                          >
                            <FeatureIcon sx={{ fontSize: 18 }} />
                          </Avatar>
                          <Typography
                            variant="subtitle2"
                            sx={{
                              fontWeight: 600,
                              color: 'text.primary',
                            }}
                          >
                            {feature.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Chip
                          label={getStatusLabel(feature.status)}
                          color={getStatusColor(feature.status) as any}
                          size="small"
                          sx={{ fontWeight: 500 }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        {feature.product ? (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ProductIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
                            <Typography variant="body2" color="text.secondary">
                              {feature.product.name}
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.disabled">
                            No product
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell sx={{ py: 2, maxWidth: 300 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {feature.description || 'No description available'}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(feature.createdAt).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Fade>
        )}
      </Content>
    </Page>
  );
};
