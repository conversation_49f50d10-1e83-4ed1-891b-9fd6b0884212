import {
  create<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@backstage/core-plugin-api';

/**
 * Product entity interface (for reference)
 */
export interface Product {
  id: string;
  name: string;
  description?: string;
}

/**
 * Feature entity interface
 */
export interface Feature {
  id: string;
  name: string;
  description?: string;
  productId: string;
  status: 'active' | 'inactive' | 'deprecated';
  createdAt: string;
  updatedAt: string;
  product?: Product;
}

/**
 * Create feature input interface
 */
export interface CreateFeatureInput {
  name: string;
  description?: string;
  productId: string;
  status?: 'active' | 'inactive' | 'deprecated';
}

/**
 * Update feature input interface
 */
export interface UpdateFeatureInput {
  name?: string;
  description?: string;
  productId?: string;
  status?: 'active' | 'inactive' | 'deprecated';
}

/**
 * API response wrapper
 */
export interface ApiResponse<T> {
  data: T;
  message: string;
}

/**
 * Features API interface
 */
export interface FeaturesApi {
  /**
   * Get all features
   */
  getFeatures(): Promise<Feature[]>;

  /**
   * Get feature by ID
   */
  getFeatureById(id: string): Promise<Feature>;

  /**
   * Create a new feature
   */
  createFeature(input: CreateFeatureInput): Promise<Feature>;

  /**
   * Update an existing feature
   */
  updateFeature(id: string, input: UpdateFeatureInput): Promise<Feature>;

  /**
   * Delete a feature
   */
  deleteFeature(id: string): Promise<void>;

  /**
   * Search features
   */
  searchFeatures(query: string): Promise<Feature[]>;

  /**
   * Get features by product ID
   */
  getFeaturesByProductId(productId: string): Promise<Feature[]>;
}

/**
 * API reference for dependency injection
 */
export const featuresApiRef = createApiRef<FeaturesApi>({
  id: 'plugin.features.service',
});

/**
 * Features API client implementation
 */
export class FeaturesClient implements FeaturesApi {
  private readonly discoveryApi: DiscoveryApi;
  private readonly fetchApi: FetchApi;

  constructor(options: { discoveryApi: DiscoveryApi; fetchApi: FetchApi }) {
    this.discoveryApi = options.discoveryApi;
    this.fetchApi = options.fetchApi;
  }

  private async getBaseUrl() {
    return `${await this.discoveryApi.getBaseUrl('features')}/features`;
  }

  async getFeatures(): Promise<Feature[]> {
    const url = await this.getBaseUrl();
    const response = await this.fetchApi.fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch features: ${response.statusText}`);
    }
    
    const result: ApiResponse<Feature[]> = await response.json();
    return result.data;
  }

  async getFeatureById(id: string): Promise<Feature> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch feature: ${response.statusText}`);
    }
    
    const result: ApiResponse<Feature> = await response.json();
    return result.data;
  }

  async createFeature(input: CreateFeatureInput): Promise<Feature> {
    const url = await this.getBaseUrl();
    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to create feature: ${response.statusText}`);
    }
    
    const result: ApiResponse<Feature> = await response.json();
    return result.data;
  }

  async updateFeature(id: string, input: UpdateFeatureInput): Promise<Feature> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to update feature: ${response.statusText}`);
    }
    
    const result: ApiResponse<Feature> = await response.json();
    return result.data;
  }

  async deleteFeature(id: string): Promise<void> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to delete feature: ${response.statusText}`);
    }
  }

  async searchFeatures(query: string): Promise<Feature[]> {
    const url = `${await this.getBaseUrl()}?search=${encodeURIComponent(query)}`;
    const response = await this.fetchApi.fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to search features: ${response.statusText}`);
    }
    
    const result: ApiResponse<Feature[]> = await response.json();
    return result.data;
  }

  async getFeaturesByProductId(productId: string): Promise<Feature[]> {
    const url = `${await this.getBaseUrl()}?product_id=${encodeURIComponent(productId)}`;
    const response = await this.fetchApi.fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch features by product: ${response.statusText}`);
    }

    const result: ApiResponse<Feature[]> = await response.json();
    return result.data;
  }
}
