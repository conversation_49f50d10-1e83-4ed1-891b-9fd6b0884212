import { createRouteRef, createSubRouteRef } from '@backstage/core-plugin-api';

export const rootRouteRef = createRouteRef({
  id: 'features',
});

export const featureRouteRef = createSubRouteRef({
  id: 'features/feature',
  parent: rootRouteRef,
  path: '/:id',
});

export const newFeatureRouteRef = createSubRouteRef({
  id: 'features/new',
  parent: rootRouteRef,
  path: '/new',
});

export const editFeatureRouteRef = createSubRouteRef({
  id: 'features/edit',
  parent: rootRouteRef,
  path: '/:id/edit',
});
