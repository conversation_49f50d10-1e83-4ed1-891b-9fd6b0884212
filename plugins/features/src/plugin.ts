import {
  createPlugin,
  createRoutableExtension,
  createApiFactory,
  discoveryApiRef,
  fetchApiRef,
} from '@backstage/core-plugin-api';

import { rootRouteRef, featureRouteRef, newFeatureRouteRef, editFeatureRouteRef } from './routes';
import { featuresApiRef, FeaturesClient } from './api';

/**
 * Features plugin definition
 */
export const featuresPlugin = createPlugin({
  id: 'features',
  apis: [
    createApiFactory({
      api: featuresApiRef,
      deps: { discoveryApi: discoveryApiRef, fetchApi: fetchApiRef },
      factory: ({ discoveryApi, fetchApi }) =>
        new FeaturesClient({ discoveryApi, fetchApi }),
    }),
  ],
  routes: {
    root: rootRouteRef,
    feature: featureRouteRef,
    newFeature: newFeatureRouteRef,
    editFeature: editFeatureRouteRef,
  },
});

/**
 * Features page extension
 */
export const FeaturesPage = featuresPlugin.provide(
  createRoutableExtension({
    name: 'FeaturesPage',
    component: () =>
      import('./components/FeaturesRouter').then(m => m.FeaturesRouter),
    mountPoint: rootRouteRef,
  }),
);
