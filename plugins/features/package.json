{"name": "@internal/plugin-features", "version": "0.1.0", "main": "src/index.ts", "types": "src/index.ts", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "backstage": {"role": "web-library", "pluginId": "features", "pluginPackages": ["@internal/plugin-features", "@internal/plugin-features-backend"]}, "sideEffects": false, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/core-components": "^0.14.10", "@backstage/core-plugin-api": "^1.9.3", "@backstage/theme": "^0.5.6", "@material-ui/core": "^4.12.2", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "^4.0.0-alpha.61", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "react": "^18.0.2", "react-dom": "^18.0.2", "react-router-dom": "^6.3.0", "react-use": "^17.2.4"}, "peerDependencies": {"react": "^16.13.1 || ^17.0.0 || ^18.0.0", "react-dom": "^16.13.1 || ^17.0.0 || ^18.0.0", "react-router-dom": "6.0.0-beta.0 || ^6.3.0"}, "devDependencies": {"@backstage/cli": "^0.26.11", "@backstage/core-app-api": "^1.12.6", "@backstage/dev-utils": "^1.0.37", "@backstage/test-utils": "^1.5.10", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/react": "^18.0.0"}, "files": ["dist"]}