{"name": "@internal/plugin-features-backend", "version": "0.1.0", "main": "src/index.ts", "types": "src/index.ts", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "backstage": {"role": "backend-plugin", "pluginId": "features", "pluginPackages": ["@internal/plugin-features", "@internal/plugin-features-backend"]}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-plugin-api": "^0.8.0", "@backstage/config": "^1.2.0", "express": "^4.17.1", "express-promise-router": "^4.1.0", "knex": "^3.1.0", "uuid": "^9.0.0", "zod": "^3.22.4"}, "devDependencies": {"@backstage/cli": "^0.26.11", "@types/express": "*", "@types/uuid": "^9.0.0"}, "files": ["dist"]}