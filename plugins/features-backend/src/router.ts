import { Router } from 'express';
import express from 'express';
import { Knex } from 'knex';
import { z } from 'zod';

import { AuthService, HttpAuthService, LoggerService } from '@backstage/backend-plugin-api';

import { FeatureStore, Feature, FeatureWithProduct } from './database/FeatureStore';
import { initializeDatabase } from './database/migrations';

/**
 * Request validation schemas
 */
const CreateFeatureSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  productId: z.string().uuid(),
  status: z.enum(['active', 'inactive', 'deprecated']).optional(),
});

const UpdateFeatureSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  productId: z.string().uuid().optional(),
  status: z.enum(['active', 'inactive', 'deprecated']).optional(),
});

/**
 * Transform feature for API response
 */
function transformFeature(feature: Feature): any {
  // Handle date conversion - database might return strings or Date objects
  const createdAt = feature.created_at instanceof Date
    ? feature.created_at.toISOString()
    : new Date(feature.created_at).toISOString();
  const updatedAt = feature.updated_at instanceof Date
    ? feature.updated_at.toISOString()
    : new Date(feature.updated_at).toISOString();

  return {
    id: feature.id,
    name: feature.name,
    description: feature.description,
    productId: feature.product_id,
    status: feature.status,
    createdAt,
    updatedAt,
  };
}

/**
 * Transform feature with product for API response
 */
function transformFeatureWithProduct(feature: FeatureWithProduct): any {
  // Handle date conversion - database might return strings or Date objects
  const createdAt = feature.created_at instanceof Date
    ? feature.created_at.toISOString()
    : new Date(feature.created_at).toISOString();
  const updatedAt = feature.updated_at instanceof Date
    ? feature.updated_at.toISOString()
    : new Date(feature.updated_at).toISOString();

  return {
    id: feature.id,
    name: feature.name,
    description: feature.description,
    productId: feature.product_id,
    status: feature.status,
    createdAt,
    updatedAt,
    product: feature.product_name ? {
      id: feature.product_id,
      name: feature.product_name,
      description: feature.product_description,
    } : undefined,
  };
}

/**
 * Router options interface
 */
export interface RouterOptions {
  logger: LoggerService;
  database: Knex;
  auth?: AuthService;
  httpAuth?: HttpAuthService;
}

/**
 * Create features router
 */
export async function createRouter(options: RouterOptions): Promise<Router> {
  const { logger, database, httpAuth } = options;
  
  // Initialize database
  await initializeDatabase(database);
  
  const featureStore = new FeatureStore(database, logger);
  const router = Router();

  // Add JSON parsing middleware
  router.use(express.json());

  // Middleware for authentication (optional - can be enabled later)
  // router.use(httpAuth.middleware());

  /**
   * GET /departments - Get all departments (for debugging and frontend use)
   */
  router.get('/departments', async (req, res) => {
    try {
      // Try to authenticate but don't require it
      if (httpAuth) {
        try {
          await httpAuth.credentials(req, { allow: ['user'] });
          logger.info('GET /departments - authenticated user');
        } catch (authError) {
          logger.info('GET /departments - unauthenticated access, allowing as guest');
        }
      }

      const departments = await database('departments')
        .select('*')
        .orderBy('name', 'asc');

      res.json({
        data: departments.map(dept => ({
          id: dept.id,
          name: dept.name,
          description: dept.description,
          createdAt: dept.created_at,
          updatedAt: dept.updated_at,
        })),
        message: 'Departments retrieved successfully',
      });
    } catch (error) {
      logger.error('Failed to get departments', error as Error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve departments',
      });
    }
  });

  /**
   * GET /features - Get all features or search features
   */
  router.get('/features', async (req, res) => {
    try {
      // Try to authenticate but don't require it
      if (httpAuth) {
        try {
          await httpAuth.credentials(req, { allow: ['user'] });
          logger.info('GET /features - authenticated user');
        } catch (authError) {
          logger.info('GET /features - unauthenticated access, allowing as guest');
        }
      }

      const { search, product_id } = req.query;
      logger.info('GET /features', { search, product_id });

      let features;
      if (search && typeof search === 'string') {
        features = await featureStore.searchFeatures(search);
      } else if (product_id && typeof product_id === 'string') {
        features = await featureStore.getFeaturesByProductId(product_id);
      } else {
        features = await featureStore.getAllFeatures();
      }

      const transformedFeatures = features.map(transformFeatureWithProduct);

      res.json({
        data: transformedFeatures,
        message: 'Features retrieved successfully',
      });
    } catch (error) {
      logger.error('Failed to get features', error as Error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve features',
      });
    }
  });

  /**
   * GET /features/:id - Get feature by ID
   */
  router.get('/features/:id', async (req, res) => {
    try {
      const { id } = req.params;
      logger.info('GET /features/:id', { id });
      
      const feature = await featureStore.getFeatureById(id);
      
      if (!feature) {
        return res.status(404).json({
          error: 'Not found',
          message: `Feature with ID '${id}' not found`,
        });
      }
      
      res.json({
        data: transformFeatureWithProduct(feature),
        message: 'Feature retrieved successfully',
      });
    } catch (error) {
      logger.error('Failed to get feature', error as Error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve feature',
      });
    }
  });

  /**
   * POST /features - Create new feature
   */
  router.post('/features', async (req, res) => {
    try {
      logger.info('POST /features', { body: req.body });

      // Check if request body exists
      if (!req.body || typeof req.body !== 'object') {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Request body is required',
          details: [
            {
              code: 'missing_body',
              message: 'Request body must be a valid JSON object',
              path: [],
            }
          ],
        });
      }

      // Validate request body
      const validation = CreateFeatureSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Invalid request data',
          details: validation.error.errors,
        });
      }

      // Map camelCase to snake_case for database
      const createData = {
        name: validation.data.name,
        description: validation.data.description,
        product_id: validation.data.productId,
        status: validation.data.status,
      };

      const feature = await featureStore.createFeature(createData);
      
      res.status(201).json({
        data: transformFeature(feature),
        message: 'Feature created successfully',
      });
    } catch (error) {
      logger.error('Failed to create feature', error as Error);
      
      // Handle specific business logic errors
      if (error instanceof Error) {
        if (error.message.includes('Product with ID') && error.message.includes('not found')) {
          return res.status(400).json({
            error: 'Business logic error',
            message: error.message,
            suggestion: 'Please ensure the product exists. You can create products at /product or check available products using GET /api/product/products',
          });
        }
        if (error.message.includes('already exists')) {
          return res.status(409).json({
            error: 'Conflict',
            message: error.message,
          });
        }
        if (error.message.includes('not found')) {
          return res.status(404).json({
            error: 'Not found',
            message: error.message,
          });
        }
      }
      
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to create feature',
      });
    }
  });

  /**
   * PUT /features/:id - Update feature
   */
  router.put('/features/:id', async (req, res) => {
    try {
      const { id } = req.params;
      logger.info('PUT /features/:id', { id, body: req.body });

      // Check if request body exists
      if (!req.body || typeof req.body !== 'object') {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Request body is required',
          details: [
            {
              code: 'missing_body',
              message: 'Request body must be a valid JSON object',
              path: [],
            }
          ],
        });
      }

      // Validate request body
      const validation = UpdateFeatureSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Invalid request data',
          details: validation.error.errors,
        });
      }

      // Map camelCase to snake_case for database
      const updateData: any = {};
      if (validation.data.name !== undefined) updateData.name = validation.data.name;
      if (validation.data.description !== undefined) updateData.description = validation.data.description;
      if (validation.data.productId !== undefined) updateData.product_id = validation.data.productId;
      if (validation.data.status !== undefined) updateData.status = validation.data.status;

      const feature = await featureStore.updateFeature(id, updateData);
      
      res.json({
        data: transformFeature(feature),
        message: 'Feature updated successfully',
      });
    } catch (error) {
      logger.error('Failed to update feature', error as Error);
      
      // Handle specific business logic errors
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          return res.status(404).json({
            error: 'Not found',
            message: error.message,
          });
        }
        if (error.message.includes('already exists')) {
          return res.status(400).json({
            error: 'Business logic error',
            message: error.message,
          });
        }
      }
      
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to update feature',
      });
    }
  });

  /**
   * DELETE /features/:id - Delete feature
   */
  router.delete('/features/:id', async (req, res) => {
    try {
      const { id } = req.params;
      logger.info('DELETE /features/:id', { id });
      
      await featureStore.deleteFeature(id);
      
      res.status(204).send();
    } catch (error) {
      logger.error('Failed to delete feature', error as Error);
      
      // Handle specific business logic errors
      if (error instanceof Error && error.message.includes('not found')) {
        return res.status(404).json({
          error: 'Not found',
          message: error.message,
        });
      }
      
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to delete feature',
      });
    }
  });

  return router;
}
