import { Knex } from 'knex';
import { v4 as uuidv4 } from 'uuid';

/**
 * Default products to create if none exist
 */
const DEFAULT_PRODUCTS = [
  {
    id: '1753619e-a945-4353-ba7b-806316e82bb8',
    name: 'Core Platform',
    description: 'Main software platform and infrastructure',
  },
  {
    id: uuidv4(),
    name: 'Mobile App',
    description: 'Mobile application for iOS and Android',
  },
  {
    id: uuidv4(),
    name: 'Analytics Dashboard',
    description: 'Business intelligence and analytics platform',
  },
  {
    id: uuidv4(),
    name: 'API Gateway',
    description: 'Centralized API management and routing',
  },
];

/**
 * Initialize the features database tables
 */
export async function initializeDatabase(database: Knex): Promise<void> {
  // First ensure products table exists (create it if it doesn't)
  const hasProductsTable = await database.schema.hasTable('products');

  if (!hasProductsTable) {
    await database.schema.createTable('products', table => {
      table.string('id').primary();
      table.string('name', 100).notNullable().unique();
      table.text('description');
      table.timestamp('created_at').defaultTo(database.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(database.fn.now()).notNullable();

      // Add indexes for better performance
      table.index(['name']);
      table.index(['created_at']);
    });

    // Seed default products
    await seedDefaultProducts(database);
  } else {
    // Check if we need to seed products (in case table exists but is empty)
    const productCount = await database('products').count('id as count').first();
    if (productCount && productCount.count === 0) {
      await seedDefaultProducts(database);
    }
  }

  // Then create features table
  const hasFeaturesTable = await database.schema.hasTable('features');

  if (!hasFeaturesTable) {
    await database.schema.createTable('features', table => {
      table.string('id').primary();
      table.string('name', 100).notNullable();
      table.text('description');
      table.string('product_id').notNullable();
      table.enum('status', ['active', 'inactive', 'deprecated']).defaultTo('active').notNullable();
      table.timestamp('created_at').defaultTo(database.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(database.fn.now()).notNullable();

      // Foreign key constraint to products table
      table.foreign('product_id').references('id').inTable('products').onDelete('CASCADE');

      // Unique constraint on name within the same product
      table.unique(['name', 'product_id']);

      // Add indexes for better performance
      table.index(['name']);
      table.index(['product_id']);
      table.index(['status']);
      table.index(['created_at']);
    });
  }
}

/**
 * Seed default products into the database
 */
async function seedDefaultProducts(database: Knex): Promise<void> {
  const now = new Date();

  const productsToInsert = DEFAULT_PRODUCTS.map(product => ({
    ...product,
    created_at: now,
    updated_at: now,
  }));

  await database('products').insert(productsToInsert);
}
