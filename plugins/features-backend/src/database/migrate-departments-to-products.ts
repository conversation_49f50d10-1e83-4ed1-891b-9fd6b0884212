import { Knex } from 'knex';
import { LoggerService } from '@backstage/backend-plugin-api';

/**
 * Migration script to convert department-based features to product-based features
 * This script should be run once to migrate existing data from the old department schema
 * to the new product schema.
 */

export interface MigrationOptions {
  database: Knex;
  logger: LoggerService;
  dryRun?: boolean;
}

export interface MigrationResult {
  success: boolean;
  migratedFeatures: number;
  migratedDepartments: number;
  errors: string[];
  warnings: string[];
}

/**
 * Migrate departments to products and update feature references
 */
export async function migrateDepartmentsToProducts(options: MigrationOptions): Promise<MigrationResult> {
  const { database, logger, dryRun = false } = options;
  
  const result: MigrationResult = {
    success: false,
    migratedFeatures: 0,
    migratedDepartments: 0,
    errors: [],
    warnings: [],
  };

  try {
    logger.info('Starting migration from departments to products', { dryRun });

    // Check if departments table exists
    const hasDepartmentsTable = await database.schema.hasTable('departments');
    if (!hasDepartmentsTable) {
      result.warnings.push('Departments table does not exist - nothing to migrate');
      result.success = true;
      return result;
    }

    // Check if products table exists
    const hasProductsTable = await database.schema.hasTable('products');
    if (!hasProductsTable) {
      result.errors.push('Products table does not exist - cannot migrate');
      return result;
    }

    // Check if features table exists
    const hasFeaturesTable = await database.schema.hasTable('features');
    if (!hasFeaturesTable) {
      result.warnings.push('Features table does not exist - nothing to migrate');
      result.success = true;
      return result;
    }

    // Start transaction
    await database.transaction(async (trx) => {
      // Step 1: Migrate departments to products
      const departments = await trx('departments').select('*');
      logger.info(`Found ${departments.length} departments to migrate`);

      for (const dept of departments) {
        // Check if product with same ID already exists
        const existingProduct = await trx('products').where('id', dept.id).first();
        
        if (existingProduct) {
          result.warnings.push(`Product with ID ${dept.id} already exists, skipping department migration`);
          continue;
        }

        if (!dryRun) {
          await trx('products').insert({
            id: dept.id,
            name: dept.name,
            description: dept.description,
            created_at: dept.created_at,
            updated_at: dept.updated_at,
          });
        }
        
        result.migratedDepartments++;
        logger.info(`Migrated department to product: ${dept.name} (${dept.id})`);
      }

      // Step 2: Check if features table has department_id column
      const tableInfo = await trx.raw("PRAGMA table_info(features)");
      const columns = tableInfo.map((col: any) => col.name);
      
      const hasDepartmentIdColumn = columns.includes('department_id');
      const hasProductIdColumn = columns.includes('product_id');

      if (!hasDepartmentIdColumn && hasProductIdColumn) {
        result.warnings.push('Features table already uses product_id - no feature migration needed');
      } else if (hasDepartmentIdColumn && !hasProductIdColumn) {
        // Step 3: Add product_id column to features table
        if (!dryRun) {
          await trx.schema.alterTable('features', (table) => {
            table.string('product_id');
          });
        }

        // Step 4: Copy department_id values to product_id
        const features = await trx('features').select('id', 'department_id');
        logger.info(`Found ${features.length} features to migrate`);

        for (const feature of features) {
          if (!dryRun) {
            await trx('features')
              .where('id', feature.id)
              .update({ product_id: feature.department_id });
          }
          result.migratedFeatures++;
        }

        // Step 5: Add foreign key constraint and unique constraint
        if (!dryRun) {
          // Note: SQLite doesn't support adding foreign key constraints to existing tables
          // This would need to be done by recreating the table
          logger.warn('Foreign key constraint for product_id needs to be added manually');
          
          // Add unique constraint
          await trx.schema.alterTable('features', (table) => {
            table.unique(['name', 'product_id']);
          });
        }

        // Step 6: Remove department_id column (optional - can be done manually later)
        logger.info('department_id column can be removed manually after verifying migration');
        result.warnings.push('department_id column should be removed manually after verifying migration');
      } else if (hasDepartmentIdColumn && hasProductIdColumn) {
        result.warnings.push('Both department_id and product_id columns exist - manual cleanup may be needed');
      }

      if (dryRun) {
        logger.info('Dry run completed - no changes made to database');
      }
    });

    result.success = true;
    logger.info('Migration completed successfully', {
      migratedDepartments: result.migratedDepartments,
      migratedFeatures: result.migratedFeatures,
      dryRun,
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    result.errors.push(`Migration failed: ${errorMessage}`);
    logger.error('Migration failed', error as Error);
  }

  return result;
}

/**
 * Rollback migration (convert products back to departments)
 * Use with caution - this will only work if the original department data still exists
 */
export async function rollbackMigration(options: MigrationOptions): Promise<MigrationResult> {
  const { database, logger, dryRun = false } = options;
  
  const result: MigrationResult = {
    success: false,
    migratedFeatures: 0,
    migratedDepartments: 0,
    errors: [],
    warnings: [],
  };

  try {
    logger.info('Starting rollback from products to departments', { dryRun });

    // Implementation would be similar but in reverse
    // This is left as an exercise and should be implemented carefully
    result.warnings.push('Rollback functionality not implemented - manual rollback required');
    result.success = true;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    result.errors.push(`Rollback failed: ${errorMessage}`);
    logger.error('Rollback failed', error as Error);
  }

  return result;
}
