import { Knex } from 'knex';
import { LoggerService } from '@backstage/backend-plugin-api';
import { v4 as uuidv4 } from 'uuid';
import fetch from 'node-fetch';

/**
 * Feature entity interface
 */
export interface Feature {
  id: string;
  name: string;
  description?: string;
  product_id: string;
  status: 'active' | 'inactive' | 'deprecated';
  created_at: Date;
  updated_at: Date;
}

/**
 * Feature with product information
 */
export interface FeatureWithProduct extends Feature {
  product_name: string;
  product_description?: string;
}

/**
 * Create feature input interface
 */
export interface CreateFeatureInput {
  name: string;
  description?: string;
  product_id: string;
  status?: 'active' | 'inactive' | 'deprecated';
}

/**
 * Update feature input interface
 */
export interface UpdateFeatureInput {
  name?: string;
  description?: string;
  product_id?: string;
  status?: 'active' | 'inactive' | 'deprecated';
}

/**
 * Feature store for database operations
 */
export class FeatureStore {
  private readonly db: Knex;
  private readonly logger: LoggerService;

  constructor(database: Knex, logger: LoggerService) {
    this.db = database;
    this.logger = logger;
  }

  /**
   * Get all features with product information
   */
  async getAllFeatures(): Promise<FeatureWithProduct[]> {
    this.logger.debug('Fetching all features');

    try {
      const features = await this.db('features')
        .leftJoin('products', 'features.product_id', 'products.id')
        .select(
          'features.*',
          'products.name as product_name',
          'products.description as product_description'
        )
        .orderBy('features.name', 'asc');

      this.logger.debug(`Found ${features.length} features`);
      return features;
    } catch (error) {
      // If tables don't exist yet, return empty array
      if (error instanceof Error && error.message.includes('no such table')) {
        this.logger.debug('Features or products table does not exist yet, returning empty array');
        return [];
      }
      throw error;
    }
  }

  /**
   * Get feature by ID with product information
   */
  async getFeatureById(id: string): Promise<FeatureWithProduct | null> {
    this.logger.debug('Fetching feature by ID', { id });

    try {
      const feature = await this.db('features')
        .leftJoin('products', 'features.product_id', 'products.id')
        .select(
          'features.*',
          'products.name as product_name',
          'products.description as product_description'
        )
        .where('features.id', id)
        .first();

      if (!feature) {
        this.logger.debug('Feature not found', { id });
        return null;
      }

      this.logger.debug('Feature found', { id, name: feature.name });
      return feature;
    } catch (error) {
      // If tables don't exist yet, return null
      if (error instanceof Error && error.message.includes('no such table')) {
        this.logger.debug('Features or products table does not exist yet', { id });
        return null;
      }
      throw error;
    }
  }

  /**
   * Create a new feature
   */
  async createFeature(input: CreateFeatureInput): Promise<Feature> {
    this.logger.debug('Creating new feature', { name: input.name, product_id: input.product_id });

    // Check if product exists, if not try to sync from product service or create placeholder
    let product = await this.db('products')
      .where('id', input.product_id)
      .first();

    if (!product) {
      // Try to sync product from product service
      try {
        await this.syncProductFromService(input.product_id);
        product = await this.db('products')
          .where('id', input.product_id)
          .first();
      } catch (syncError) {
        this.logger.warn(`Failed to sync product ${input.product_id}`, syncError as Error);

        // Create a placeholder product as fallback
        try {
          await this.createPlaceholderProduct(input.product_id);
          product = await this.db('products')
            .where('id', input.product_id)
            .first();
          this.logger.info(`Created placeholder product for ${input.product_id}`);
        } catch (placeholderError) {
          this.logger.error(`Failed to create placeholder product`, placeholderError as Error);
        }
      }

      if (!product) {
        throw new Error(`Product with ID '${input.product_id}' not found`);
      }
    }

    // Check if feature with same name already exists in the same product
    const existingFeature = await this.db('features')
      .where('name', input.name)
      .where('product_id', input.product_id)
      .first();

    if (existingFeature) {
      throw new Error(`Feature with name '${input.name}' already exists in this product`);
    }

    const id = uuidv4();
    const now = new Date();

    const feature: Feature = {
      id,
      name: input.name,
      description: input.description,
      product_id: input.product_id,
      status: input.status || 'active',
      created_at: now,
      updated_at: now,
    };

    await this.db('features').insert(feature);

    this.logger.info('Feature created successfully', { id, name: input.name, product_id: input.product_id });
    return feature;
  }

  /**
   * Update an existing feature
   */
  async updateFeature(id: string, input: UpdateFeatureInput): Promise<Feature> {
    this.logger.debug('Updating feature', { id });

    // Check if feature exists
    const existingFeature = await this.getFeatureById(id);
    if (!existingFeature) {
      throw new Error(`Feature with ID '${id}' not found`);
    }

    // Check if product exists (if product_id is being updated)
    if (input.product_id && input.product_id !== existingFeature.product_id) {
      const product = await this.db('products')
        .where('id', input.product_id)
        .first();

      if (!product) {
        throw new Error(`Product with ID '${input.product_id}' not found`);
      }
    }

    // Check if name is being changed and if new name already exists in the target product
    if (input.name && input.name !== existingFeature.name) {
      const targetProductId = input.product_id || existingFeature.product_id;
      const featureWithSameName = await this.db('features')
        .where('name', input.name)
        .where('product_id', targetProductId)
        .whereNot('id', id)
        .first();

      if (featureWithSameName) {
        throw new Error(`Feature with name '${input.name}' already exists in the target product`);
      }
    }
    
    const updateData = {
      ...input,
      updated_at: new Date(),
    };
    
    await this.db('features')
      .where('id', id)
      .update(updateData);
    
    const updatedFeature = await this.db('features')
      .where('id', id)
      .first();
    
    this.logger.info('Feature updated successfully', { id });
    return updatedFeature;
  }

  /**
   * Delete a feature
   */
  async deleteFeature(id: string): Promise<void> {
    this.logger.debug('Deleting feature', { id });
    
    // Check if feature exists
    const existingFeature = await this.getFeatureById(id);
    if (!existingFeature) {
      throw new Error(`Feature with ID '${id}' not found`);
    }
    
    await this.db('features')
      .where('id', id)
      .del();
    
    this.logger.info('Feature deleted successfully', { id });
  }

  /**
   * Search features by name or description
   */
  async searchFeatures(query: string): Promise<FeatureWithProduct[]> {
    this.logger.debug('Searching features', { query });

    try {
      // Use LIKE for SQLite compatibility (case-insensitive search)
      const searchPattern = `%${query.toLowerCase()}%`;

      const features = await this.db('features')
        .leftJoin('products', 'features.product_id', 'products.id')
        .select(
          'features.*',
          'products.name as product_name',
          'products.description as product_description'
        )
        .whereRaw('LOWER(features.name) LIKE ?', [searchPattern])
        .orWhereRaw('LOWER(features.description) LIKE ?', [searchPattern])
        .orderBy('features.name', 'asc');

      this.logger.debug(`Found ${features.length} features matching query`, { query });
      return features;
    } catch (error) {
      // If tables don't exist yet, return empty array
      if (error instanceof Error && error.message.includes('no such table')) {
        this.logger.debug('Features or products table does not exist yet, returning empty array for search', { query });
        return [];
      }
      throw error;
    }
  }

  /**
   * Get features by product ID
   */
  async getFeaturesByProductId(productId: string): Promise<FeatureWithProduct[]> {
    this.logger.debug('Fetching features by product ID', { productId });

    try {
      const features = await this.db('features')
        .leftJoin('products', 'features.product_id', 'products.id')
        .select(
          'features.*',
          'products.name as product_name',
          'products.description as product_description'
        )
        .where('features.product_id', productId)
        .orderBy('features.name', 'asc');

      this.logger.debug(`Found ${features.length} features for product`, { productId });
      return features;
    } catch (error) {
      // If tables don't exist yet, return empty array
      if (error instanceof Error && error.message.includes('no such table')) {
        this.logger.debug('Features or products table does not exist yet, returning empty array for product', { productId });
        return [];
      }
      throw error;
    }
  }

  /**
   * Sync a product from the product service to the local database
   */
  private async syncProductFromService(productId: string): Promise<void> {
    this.logger.debug('Syncing product from service', { productId });

    try {
      // First try to get all products to see if the product exists
      const allProductsResponse = await fetch(`http://localhost:7007/api/product/products`);

      if (!allProductsResponse.ok) {
        throw new Error(`Product service returned ${allProductsResponse.status}: ${allProductsResponse.statusText}`);
      }

      const allProductsResult = await allProductsResponse.json();
      const products = allProductsResult.data || [];

      // Find the specific product
      const productData = products.find((prod: any) => prod.id === productId);

      if (!productData) {
        throw new Error(`Product with ID ${productId} not found in product service`);
      }

      // Insert the product into our local database
      const now = new Date();
      await this.db('products').insert({
        id: productData.id,
        name: productData.name,
        description: productData.description,
        created_at: new Date(productData.createdAt || now),
        updated_at: new Date(productData.updatedAt || now),
      });

      this.logger.info('Successfully synced product from service', {
        productId,
        name: productData.name
      });

    } catch (error) {
      this.logger.error('Failed to sync product from service', {
        productId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Create a placeholder product when the real one cannot be found
   */
  private async createPlaceholderProduct(productId: string): Promise<void> {
    this.logger.debug('Creating placeholder product', { productId });

    const now = new Date();
    await this.db('products').insert({
      id: productId,
      name: `Product ${productId.substring(0, 8)}...`,
      description: 'Placeholder product created automatically',
      created_at: now,
      updated_at: now,
    });

    this.logger.info('Created placeholder product', { productId });
  }
}
