import { <PERSON><PERSON> } from 'knex';
import { LoggerService } from '@backstage/backend-plugin-api';
import { v4 as uuidv4 } from 'uuid';

export interface Collection {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface CollectionVersion {
  id: string;
  collection_id: string;
  version: number;
  content: string; // JSON data stored as string
  created_at: string;
  user_id: string;
}

export class DatabaseHandler {
  private readonly db: Knex;
  private readonly logger: LoggerService;

  constructor(db: Knex, logger: LoggerService) {
    this.db = db;
    this.logger = logger;
  }

  async createTables() {
    const hasCollectionsTable = await this.db.schema.hasTable('collections');
    const hasCollectionVersionsTable = await this.db.schema.hasTable('collection_versions');

    if (!hasCollectionsTable) {
      this.logger.info('Creating collections table');
      await this.db.schema.createTable('collections', table => {
        table.string('id').primary();
        table.string('name').notNullable();
        table.text('description');
        table.string('owner_id').notNullable();
        table.timestamp('created_at').defaultTo(this.db.fn.now());
        table.timestamp('updated_at').defaultTo(this.db.fn.now());
      });
    }

    if (!hasCollectionVersionsTable) {
      this.logger.info('Creating collection_versions table');
      await this.db.schema.createTable('collection_versions', table => {
        table.string('id').primary();
        table.string('collection_id').notNullable();
        table.integer('version').notNullable();
        table.text('content').notNullable();
        table.timestamp('created_at').defaultTo(this.db.fn.now());
        table.string('user_id').notNullable();
        
        table.foreign('collection_id').references('id').inTable('collections').onDelete('CASCADE');
        table.unique(['collection_id', 'version']);
      });
    }
  }

  // Collection operations
  async createCollection(name: string, description: string, ownerId: string, content: string): Promise<Collection> {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const collection: Collection = {
      id,
      name,
      description,
      owner_id: ownerId,
      created_at: now,
      updated_at: now,
    };

    await this.db.transaction(async trx => {
      await trx('collections').insert(collection);
      
      // Create initial version (v1)
      const version: CollectionVersion = {
        id: uuidv4(),
        collection_id: id,
        version: 1,
        content,
        created_at: now,
        user_id: ownerId,
      };
      
      await trx('collection_versions').insert(version);
    });

    return collection;
  }

  async getCollections(userId: string, isAdmin: boolean): Promise<Collection[]> {
    if (isAdmin) {
      return this.db('collections').select('*');
    }
    return this.db('collections').where({ owner_id: userId }).select('*');
  }

  async getCollectionById(id: string, userId: string, isAdmin: boolean): Promise<Collection | undefined> {
    const query = this.db('collections').where({ id });
    
    if (!isAdmin) {
      query.andWhere({ owner_id: userId });
    }
    
    return query.first();
  }

  async updateCollection(
    id: string, 
    userId: string, 
    isAdmin: boolean, 
    updates: { name?: string; description?: string; content?: string }
  ): Promise<Collection | undefined> {
    const collection = await this.getCollectionById(id, userId, isAdmin);
    
    if (!collection) {
      return undefined;
    }

    const collectionUpdates: Partial<Collection> = {
      updated_at: new Date().toISOString(),
    };

    if (updates.name) {
      collectionUpdates.name = updates.name;
    }

    if (updates.description) {
      collectionUpdates.description = updates.description;
    }

    await this.db.transaction(async trx => {
      await trx('collections').where({ id }).update(collectionUpdates);
      
      if (updates.content) {
        // Get the latest version number
        const latestVersion = await trx('collection_versions')
          .where({ collection_id: id })
          .max('version as maxVersion')
          .first();
        
        const newVersion = (latestVersion?.maxVersion || 0) + 1;
        
        // Create new version
        const version: CollectionVersion = {
          id: uuidv4(),
          collection_id: id,
          version: newVersion,
          content: updates.content,
          created_at: new Date().toISOString(),
          user_id: userId,
        };
        
        await trx('collection_versions').insert(version);
      }
    });

    return this.getCollectionById(id, userId, isAdmin);
  }

  async deleteCollection(id: string, userId: string, isAdmin: boolean): Promise<boolean> {
    const collection = await this.getCollectionById(id, userId, isAdmin);
    
    if (!collection) {
      return false;
    }

    await this.db('collections').where({ id }).delete();
    return true;
  }

  // Version operations
  async getCollectionVersions(collectionId: string, userId: string, isAdmin: boolean): Promise<CollectionVersion[]> {
    const collection = await this.getCollectionById(collectionId, userId, isAdmin);
    
    if (!collection) {
      return [];
    }

    return this.db('collection_versions')
      .where({ collection_id: collectionId })
      .orderBy('version', 'desc')
      .select('*');
  }

  async getCollectionVersion(
    collectionId: string, 
    versionNumber: number, 
    userId: string, 
    isAdmin: boolean
  ): Promise<CollectionVersion | undefined> {
    const collection = await this.getCollectionById(collectionId, userId, isAdmin);
    
    if (!collection) {
      return undefined;
    }

    return this.db('collection_versions')
      .where({ collection_id: collectionId, version: versionNumber })
      .first();
  }

  async rollbackToVersion(
    collectionId: string, 
    versionNumber: number, 
    userId: string, 
    isAdmin: boolean
  ): Promise<CollectionVersion | undefined> {
    const collection = await this.getCollectionById(collectionId, userId, isAdmin);
    
    if (!collection) {
      return undefined;
    }

    const targetVersion = await this.getCollectionVersion(collectionId, versionNumber, userId, isAdmin);
    
    if (!targetVersion) {
      return undefined;
    }

    // Get the latest version number
    const latestVersion = await this.db('collection_versions')
      .where({ collection_id: collectionId })
      .max('version as maxVersion')
      .first();
    
    const newVersion = (latestVersion?.maxVersion || 0) + 1;
    
    // Create new version based on the target version
    const version: CollectionVersion = {
      id: uuidv4(),
      collection_id: collectionId,
      version: newVersion,
      content: targetVersion.content,
      created_at: new Date().toISOString(),
      user_id: userId,
    };
    
    await this.db('collection_versions').insert(version);
    
    // Update collection's updated_at timestamp
    await this.db('collections')
      .where({ id: collectionId })
      .update({ updated_at: new Date().toISOString() });

    return version;
  }
}
