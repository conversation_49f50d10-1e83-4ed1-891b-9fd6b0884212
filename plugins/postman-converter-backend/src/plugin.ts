import {
  coreServices,
  createBackendPlugin,
} from '@backstage/backend-plugin-api';
import { createRouter } from './router';

/**
 * postmanConverterPlugin backend plugin
 *
 * @public
 */
export const postmanConverterPlugin = createBackendPlugin({
  pluginId: 'postman-converter',
  register(env) {
    env.registerInit({
      deps: {
        logger: coreServices.logger,
        auth: coreServices.auth,
        httpAuth: coreServices.httpAuth,
        httpRouter: coreServices.httpRouter,
        database: coreServices.database,
      },
      async init({ logger, auth, httpAuth, httpRouter, database }) {
        httpRouter.use(
          await createRouter({
            auth,
            httpAuth,
            database: await database.getClient(),
            logger,
          }),
        );
      },
    });
  },
});
