import { Knex } from 'knex';
import { LoggerService } from '@backstage/backend-plugin-api';
import { v4 as uuidv4 } from 'uuid';

/**
 * Product entity interface
 */
export interface Product {
  id: string;
  name: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Create product input interface
 */
export interface CreateProductInput {
  name: string;
  description?: string;
}

/**
 * Update product input interface
 */
export interface UpdateProductInput {
  name?: string;
  description?: string;
}

/**
 * Product store for database operations
 */
export class ProductStore {
  private readonly db: Knex;
  private readonly logger: LoggerService;

  constructor(database: Knex, logger: LoggerService) {
    this.db = database;
    this.logger = logger;
  }

  /**
   * Get all products
   */
  async getAllProducts(): Promise<Product[]> {
    this.logger.debug('Fetching all products');

    const products = await this.db('products')
      .select('*')
      .orderBy('name', 'asc');

    this.logger.debug(`Found ${products.length} products`);
    return products;
  }

  /**
   * Get product by ID
   */
  async getProductById(id: string): Promise<Product | null> {
    this.logger.debug('Fetching product by ID', { id });

    const product = await this.db('products')
      .where('id', id)
      .first();

    if (!product) {
      this.logger.debug('Product not found', { id });
      return null;
    }

    this.logger.debug('Product found', { id, name: product.name });
    return product;
  }

  /**
   * Create a new product
   */
  async createProduct(input: CreateProductInput): Promise<Product> {
    this.logger.debug('Creating new product', { name: input.name });

    // Check if product with same name already exists
    const existingProduct = await this.db('products')
      .where('name', input.name)
      .first();

    if (existingProduct) {
      throw new Error(`Product with name '${input.name}' already exists`);
    }

    const id = uuidv4();
    const now = new Date();

    const product: Product = {
      id,
      name: input.name,
      description: input.description,
      created_at: now,
      updated_at: now,
    };

    await this.db('products').insert(product);

    this.logger.info('Product created successfully', { id, name: input.name });
    return product;
  }

  /**
   * Update an existing product
   */
  async updateProduct(id: string, input: UpdateProductInput): Promise<Product> {
    this.logger.debug('Updating product', { id });

    // Check if product exists
    const existingProduct = await this.getProductById(id);
    if (!existingProduct) {
      throw new Error(`Product with ID '${id}' not found`);
    }

    // Check if name is being changed and if new name already exists
    if (input.name && input.name !== existingProduct.name) {
      const productWithSameName = await this.db('products')
        .where('name', input.name)
        .whereNot('id', id)
        .first();

      if (productWithSameName) {
        throw new Error(`Product with name '${input.name}' already exists`);
      }
    }

    const updateData = {
      ...input,
      updated_at: new Date(),
    };

    await this.db('products')
      .where('id', id)
      .update(updateData);

    const updatedProduct = await this.getProductById(id);

    this.logger.info('Product updated successfully', { id, name: updatedProduct?.name });
    return updatedProduct!;
  }

  /**
   * Delete a product
   */
  async deleteProduct(id: string): Promise<void> {
    this.logger.debug('Deleting product', { id });

    // Check if product exists
    const existingProduct = await this.getProductById(id);
    if (!existingProduct) {
      throw new Error(`Product with ID '${id}' not found`);
    }

    await this.db('products')
      .where('id', id)
      .delete();

    this.logger.info('Product deleted successfully', { id, name: existingProduct.name });
  }

  /**
   * Search products by name
   */
  async searchProducts(query: string): Promise<Product[]> {
    this.logger.debug('Searching products', { query });

    // Use LIKE for SQLite compatibility (case-insensitive search)
    const searchPattern = `%${query.toLowerCase()}%`;

    const products = await this.db('products')
      .whereRaw('LOWER(name) LIKE ?', [searchPattern])
      .orWhereRaw('LOWER(description) LIKE ?', [searchPattern])
      .orderBy('name', 'asc');

    this.logger.debug(`Found ${products.length} products matching query`, { query });
    return products;
  }
}
