import { Router } from 'express';
import express from 'express';
import { Knex } from 'knex';
import { z } from 'zod';

import { AuthService, HttpAuthService, LoggerService } from '@backstage/backend-plugin-api';

import { ProductStore } from './database/ProductStore';
import { initializeDatabase } from './database/migrations';

/**
 * Request validation schemas
 */
const CreateProductSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
});

const UpdateProductSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
});

/**
 * Response transformation
 */
function transformProduct(product: any) {
  const formatDate = (date: any) => {
    if (!date) return new Date().toISOString();
    if (typeof date === 'string') return date;
    if (date instanceof Date) return date.toISOString();
    return new Date(date).toISOString();
  };

  return {
    id: product.id,
    name: product.name,
    description: product.description,
    createdAt: formatDate(product.created_at),
    updatedAt: formatDate(product.updated_at),
  };
}

/**
 * Router options
 */
export interface RouterOptions {
  logger: LoggerService;
  database: Knex;
  auth: AuthService;
  httpAuth: HttpAuthService;
}

/**
 * Create product router
 */
export async function createRouter(options: RouterOptions): Promise<Router> {
  const { logger, database } = options;

  // Initialize database
  await initializeDatabase(database);

  const productStore = new ProductStore(database, logger);
  const router = Router();

  // Add JSON parsing middleware
  router.use(express.json());

  // Middleware for authentication (optional - can be enabled later)
  // router.use(httpAuth.middleware());

  /**
   * GET /products - Get all products or search products
   */
  router.get('/products', async (req, res) => {
    try {
      const { search } = req.query;
      logger.info('GET /products', { search });

      let products;
      if (search && typeof search === 'string') {
        products = await productStore.searchProducts(search);
      } else {
        products = await productStore.getAllProducts();
      }

      const transformedProducts = products.map(transformProduct);

      res.json({
        data: transformedProducts,
        message: 'Products retrieved successfully',
      });
    } catch (error) {
      logger.error('Failed to get products', error as Error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve products',
      });
    }
  });

  /**
   * GET /products/:id - Get product by ID
   */
  router.get('/products/:id', async (req, res) => {
    try {
      const { id } = req.params;
      logger.info('GET /products/:id', { id });

      const product = await productStore.getProductById(id);

      if (!product) {
        return res.status(404).json({
          error: 'Not found',
          message: `Product with ID '${id}' not found`,
        });
      }

      res.json({
        data: transformProduct(product),
        message: 'Product retrieved successfully',
      });
    } catch (error) {
      logger.error('Failed to get product', error as Error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve product',
      });
    }
  });

  /**
   * POST /products - Create new product
   */
  router.post('/products', async (req, res) => {
    try {
      logger.info('POST /products', { body: req.body });

      // Check if request body exists
      if (!req.body || typeof req.body !== 'object') {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Request body is required',
          details: [
            {
              code: 'missing_body',
              message: 'Request body must be a valid JSON object',
              path: [],
            }
          ],
        });
      }

      // Validate request body
      const validation = CreateProductSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Invalid request data',
          details: validation.error.errors,
        });
      }

      const product = await productStore.createProduct(validation.data);

      res.status(201).json({
        data: transformProduct(product),
        message: 'Product created successfully',
      });
    } catch (error) {
      logger.error('Failed to create product', error as Error);

      if (error instanceof Error && error.message.includes('already exists')) {
        return res.status(409).json({
          error: 'Conflict',
          message: error.message,
        });
      }

      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to create product',
      });
    }
  });

  /**
   * PUT /products/:id - Update product
   */
  router.put('/products/:id', async (req, res) => {
    try {
      const { id } = req.params;
      logger.info('PUT /products/:id', { id, body: req.body });

      // Validate request body
      const validation = UpdateProductSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Invalid request data',
          details: validation.error.errors,
        });
      }

      const product = await productStore.updateProduct(id, validation.data);

      res.json({
        data: transformProduct(product),
        message: 'Product updated successfully',
      });
    } catch (error) {
      logger.error('Failed to update product', error as Error);

      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          return res.status(404).json({
            error: 'Not found',
            message: error.message,
          });
        }

        if (error.message.includes('already exists')) {
          return res.status(409).json({
            error: 'Conflict',
            message: error.message,
          });
        }
      }

      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to update product',
      });
    }
  });

  /**
   * DELETE /products/:id - Delete product
   */
  router.delete('/products/:id', async (req, res) => {
    try {
      const { id } = req.params;
      logger.info('DELETE /products/:id', { id });

      await productStore.deleteProduct(id);

      res.status(204).send();
    } catch (error) {
      logger.error('Failed to delete product', error as Error);

      if (error instanceof Error && error.message.includes('not found')) {
        return res.status(404).json({
          error: 'Not found',
          message: error.message,
        });
      }

      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to delete product',
      });
    }
  });

  return router;
}
