import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Box } from '@mui/material';
import {
  Header,
  Page,
  Content,
  ContentHeader,
  SupportButton,
  Progress,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';

import { productApiRef, Product, UpdateProductInput } from '../../api';
import { ProductForm } from '../ProductForm';

/**
 * Page for editing an existing product
 */
export const ProductEditPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const productApi = useApi(productApiRef);

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      loadProduct(id);
    }
  }, [id]);

  const loadProduct = async (productId: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await productApi.getProductById(productId);
      setProduct(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load product');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: UpdateProductInput) => {
    if (!product) return;

    try {
      setSubmitting(true);
      setError(null);

      const updatedProduct = await productApi.updateProduct(product.id, data);

      // Navigate to the updated product's detail page
      navigate(`/product/${updatedProduct.id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update product');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (product) {
      navigate(`/product/${product.id}`);
    } else {
      navigate('/product');
    }
  };

  if (loading) {
    return (
      <Page themeId="tool">
        <Header title="Products" subtitle="Manage your organization's products">
          <SupportButton>Edit product details</SupportButton>
        </Header>
        <Content>
          <Progress />
        </Content>
      </Page>
    );
  }

  if (error && !product) {
    return (
      <Page themeId="tool">
        <Header title="Products" subtitle="Manage your organization's products">
          <SupportButton>Edit product details</SupportButton>
        </Header>
        <Content>
          <ErrorPanel error={new Error(error)} />
        </Content>
      </Page>
    );
  }

  if (!product) {
    return (
      <Page themeId="tool">
        <Header title="Products" subtitle="Manage your organization's products">
          <SupportButton>Edit product details</SupportButton>
        </Header>
        <Content>
          <ErrorPanel error={new Error("Product not found")} />
        </Content>
      </Page>
    );
  }

  return (
    <Page themeId="tool">
      <Header title="Products" subtitle="Manage your organization's products">
        <SupportButton>Edit product details</SupportButton>
      </Header>
      <Content>
        <ContentHeader title={`Edit ${product.name}`} />

        <Box sx={{ maxWidth: 800, mx: 'auto', mt: 2 }}>
          <ProductForm
            product={product}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={submitting}
            error={error}
          />
        </Box>
      </Content>
    </Page>
  );
};
