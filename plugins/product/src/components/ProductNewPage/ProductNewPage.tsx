import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box } from '@mui/material';
import {
  Header,
  Page,
  Content,
  ContentHeader,
  SupportButton,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';

import { productApiRef, CreateProductInput, UpdateProductInput } from '../../api';
import { ProductForm } from '../ProductForm';

/**
 * Page for creating a new product
 */
export const ProductNewPage = () => {
  const navigate = useNavigate();
  const productApi = useApi(productApiRef);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (data: CreateProductInput | UpdateProductInput) => {
    try {
      setLoading(true);
      setError(null);

      const newProduct = await productApi.createProduct(data);

      // Navigate to the new product's detail page
      navigate(`/product/${newProduct.id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create product');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/product');
  };

  return (
    <Page themeId="tool">
      <Header title="Products" subtitle="Manage your organization's products">
        <SupportButton>Create a new product for your organization</SupportButton>
      </Header>
      <Content>
        <ContentHeader title="Create New Product" />

        <Box sx={{ maxWidth: 800, mx: 'auto', mt: 2 }}>
          <ProductForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={loading}
            error={error}
          />
        </Box>
      </Content>
    </Page>
  );
};
