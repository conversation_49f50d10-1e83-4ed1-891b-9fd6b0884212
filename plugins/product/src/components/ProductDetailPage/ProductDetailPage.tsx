import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Paper,
  Typography,
  Chip,
  Grid,
  Divider,
  Avatar,
  Stack,
  Card,
  CardContent,
  Fade,
  Skeleton,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  Business as BusinessIcon,
  AccessTime as AccessTimeIcon,
  Update as UpdateIcon,
  Fingerprint as FingerprintIcon,
} from '@mui/icons-material';
import {
  Header,
  Page,
  Content,
  ContentHeader,
  SupportButton,
  Progress,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';

import { productApiRef, Product } from '../../api';
import { DeleteConfirmationDialog } from '../DeleteConfirmationDialog';

/**
 * Page for viewing product details
 */
export const ProductDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const productApi = useApi(productApiRef);

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialog, setDeleteDialog] = useState(false);

  useEffect(() => {
    if (id) {
      loadProduct(id);
    }
  }, [id]);

  const loadProduct = async (productId: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await productApi.getProductById(productId);
      setProduct(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load product');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = () => {
    setDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!product) return;

    try {
      await productApi.deleteProduct(product.id);
      navigate('/product');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete product');
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialog(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Page themeId="tool">
        <Header title="Products" subtitle="Manage your organization's products">
          <SupportButton>View product details</SupportButton>
        </Header>
        <Content>
          <ContentHeader title="">
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Skeleton variant="rectangular" width={120} height={36} />
              <Skeleton variant="rectangular" width={80} height={36} />
              <Skeleton variant="rectangular" width={90} height={36} />
            </Box>
          </ContentHeader>

          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: 3, borderRadius: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Skeleton variant="circular" width={64} height={64} sx={{ mr: 2 }} />
                  <Box sx={{ flexGrow: 1 }}>
                    <Skeleton variant="text" width="60%" height={40} />
                    <Skeleton variant="rectangular" width={120} height={24} sx={{ mt: 1 }} />
                  </Box>
                </Box>
                <Divider sx={{ my: 3 }} />
                <Skeleton variant="text" width="30%" height={32} />
                <Skeleton variant="text" width="100%" height={24} sx={{ mt: 1 }} />
                <Skeleton variant="text" width="80%" height={24} />
                <Skeleton variant="text" width="90%" height={24} />
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, borderRadius: 3 }}>
                <Skeleton variant="text" width="70%" height={32} />
                {[...Array(3)].map((_, index) => (
                  <Box key={index} sx={{ mb: 2, mt: 2 }}>
                    <Skeleton variant="text" width="50%" height={20} />
                    <Skeleton variant="text" width="80%" height={24} />
                  </Box>
                ))}
              </Paper>
            </Grid>
          </Grid>
        </Content>
      </Page>
    );
  }

  if (error) {
    return (
      <Page themeId="tool">
        <Header title="Products" subtitle="Manage your organization's products">
          <SupportButton>View product details</SupportButton>
        </Header>
        <Content>
          <ErrorPanel error={new Error(error)} />
        </Content>
      </Page>
    );
  }

  if (!product) {
    return (
      <Page themeId="tool">
        <Header title="Products" subtitle="Manage your organization's products">
          <SupportButton>View product details</SupportButton>
        </Header>
        <Content>
          <ErrorPanel error={new Error("Product not found")} />
        </Content>
      </Page>
    );
  }

  return (
    <Page themeId="tool">
      <Header title="Products" subtitle="Manage your organization's products">
        <SupportButton>View product details</SupportButton>
      </Header>
      <Content>
        <ContentHeader title={product.name}>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            alignItems="center"
            sx={{ width: { xs: '100%', sm: 'auto' } }}
          >
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/product')}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 500,
                minWidth: { xs: '100%', sm: 'auto' },
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 2,
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              Back to List
            </Button>
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={() => navigate(`/product/${product.id}/edit`)}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 600,
                minWidth: { xs: '100%', sm: 'auto' },
                boxShadow: 2,
                '&:hover': {
                  boxShadow: 4,
                  transform: 'translateY(-1px)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              Edit Product
            </Button>
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDeleteClick}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 500,
                minWidth: { xs: '100%', sm: 'auto' },
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 2,
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              Delete
            </Button>
          </Stack>
        </ContentHeader>

        <Fade in timeout={600}>
          <Grid container spacing={3} sx={{ alignItems: 'stretch' }}>
            <Grid item xs={12} md={8}>
              <Paper
                sx={{
                  p: 4,
                  borderRadius: 3,
                  boxShadow: 3,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,1) 100%)',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                  <Avatar
                    sx={{
                      width: 80,
                      height: 80,
                      bgcolor: 'primary.main',
                      mr: 3,
                      boxShadow: 3,
                      flexShrink: 0,
                    }}
                  >
                    <BusinessIcon sx={{ fontSize: 40 }} />
                  </Avatar>
                  <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                    <Typography
                      variant="h3"
                      component="h1"
                      gutterBottom
                      sx={{
                        fontWeight: 700,
                        color: 'text.primary',
                        mb: 1,
                        lineHeight: 1.2,
                        wordBreak: 'break-word',
                      }}
                    >
                      {product.name}
                    </Typography>
                    <Chip
                      icon={<FingerprintIcon />}
                      label={`ID: ${product.id.slice(0, 8)}...`}
                      size="medium"
                      variant="outlined"
                      sx={{
                        borderRadius: 2,
                        fontFamily: 'monospace',
                        '& .MuiChip-icon': {
                          fontSize: 16,
                        },
                      }}
                    />
                  </Box>
                </Box>

                <Divider sx={{ my: 4 }} />

                <Box sx={{ flexGrow: 1 }}>
                  <Typography
                    variant="h5"
                    gutterBottom
                    sx={{
                      fontWeight: 600,
                      color: 'text.primary',
                      mb: 3,
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    Description
                  </Typography>
                  {product.description ? (
                    <Paper
                      sx={{
                        p: 3,
                        bgcolor: 'grey.50',
                        borderRadius: 2,
                        border: '1px solid',
                        borderColor: 'grey.200',
                        height: '100%',
                        minHeight: 120,
                      }}
                    >
                      <Typography
                        variant="body1"
                        sx={{
                          lineHeight: 1.8,
                          color: 'text.secondary',
                          fontSize: '1.1rem',
                          wordBreak: 'break-word',
                        }}
                      >
                        {product.description}
                      </Typography>
                    </Paper>
                  ) : (
                    <Paper
                      sx={{
                        p: 3,
                        bgcolor: 'grey.50',
                        borderRadius: 2,
                        border: '1px dashed',
                        borderColor: 'grey.300',
                        textAlign: 'center',
                        height: '100%',
                        minHeight: 120,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography
                        variant="body1"
                        color="text.disabled"
                        fontStyle="italic"
                        sx={{ fontSize: '1.1rem' }}
                      >
                        No description provided
                      </Typography>
                    </Paper>
                  )}
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} md={4}>
              <Stack spacing={3} sx={{ height: '100%' }}>
                <Card
                  sx={{
                    borderRadius: 3,
                    boxShadow: 3,
                    overflow: 'hidden',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1 }}>
                    <Typography
                      variant="h6"
                      gutterBottom
                      sx={{
                        fontWeight: 600,
                        color: 'text.primary',
                        mb: 3,
                        textAlign: 'center',
                      }}
                    >
                      Product Information
                    </Typography>

                    <Stack spacing={3} sx={{ height: '100%' }}>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                          <AccessTimeIcon sx={{ mr: 1.5, fontSize: 20, color: 'primary.main' }} />
                          <Typography variant="subtitle2" color="text.secondary" fontWeight={600}>
                            Created
                          </Typography>
                        </Box>
                        <Typography
                          variant="body1"
                          sx={{
                            pl: 4,
                            fontWeight: 500,
                            color: 'text.primary',
                          }}
                        >
                          {formatDate(product.createdAt)}
                        </Typography>
                      </Box>

                      <Divider />

                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                          <UpdateIcon sx={{ mr: 1.5, fontSize: 20, color: 'primary.main' }} />
                          <Typography variant="subtitle2" color="text.secondary" fontWeight={600}>
                            Last Updated
                          </Typography>
                        </Box>
                        <Typography
                          variant="body1"
                          sx={{
                            pl: 4,
                            fontWeight: 500,
                            color: 'text.primary',
                          }}
                        >
                          {formatDate(product.updatedAt)}
                        </Typography>
                      </Box>

                      <Divider />

                      <Box sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                          <FingerprintIcon sx={{ mr: 1.5, fontSize: 20, color: 'primary.main' }} />
                          <Typography variant="subtitle2" color="text.secondary" fontWeight={600}>
                            Product ID
                          </Typography>
                        </Box>
                        <Paper
                          sx={{
                            p: 2,
                            ml: 4,
                            bgcolor: 'grey.100',
                            borderRadius: 2,
                            border: '1px solid',
                            borderColor: 'grey.300',
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              fontSize: '0.875rem',
                              wordBreak: 'break-all',
                              color: 'text.primary',
                              lineHeight: 1.4,
                            }}
                          >
                            {product.id}
                          </Typography>
                        </Paper>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Stack>
            </Grid>
          </Grid>
        </Fade>

        <DeleteConfirmationDialog
          open={deleteDialog}
          product={product}
          onConfirm={handleDeleteConfirm}
          onCancel={handleDeleteCancel}
        />
      </Content>
    </Page>
  );
};
