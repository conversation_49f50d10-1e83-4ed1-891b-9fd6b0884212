import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Paper,
  Typography,
  Alert,
  Stack,
  Divider,
  Avatar,
  Fade,
  LinearProgress,
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Business as BusinessIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';

import { Product, CreateProductInput, UpdateProductInput } from '../../api';

interface ProductFormProps {
  product?: Product;
  onSubmit: (data: CreateProductInput | UpdateProductInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

/**
 * Reusable form component for creating and editing products
 */
export const ProductForm: React.FC<ProductFormProps> = ({
  product,
  onSubmit,
  onCancel,
  loading = false,
  error = null,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    description?: string;
  }>({});

  const isEditing = Boolean(product);

  // Initialize form data when product prop changes
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        description: product.description || '',
      });
    } else {
      setFormData({
        name: '',
        description: '',
      });
    }
  }, [product]);

  const validateForm = () => {
    const errors: { name?: string; description?: string } = {};

    if (!formData.name.trim()) {
      errors.name = 'Product name is required';
    } else if (formData.name.length > 100) {
      errors.name = 'Product name must be 100 characters or less';
    }

    if (formData.description && formData.description.length > 500) {
      errors.description = 'Description must be 500 characters or less';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    const submitData = {
      name: formData.name.trim(),
      description: formData.description.trim() || undefined,
    };

    await onSubmit(submitData);
  };

  const handleInputChange = (field: keyof typeof formData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  return (
    <Fade in timeout={600}>
      <Paper
        sx={{
          p: 4,
          borderRadius: 3,
          boxShadow: 3,
          background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,1) 100%)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <Avatar
            sx={{
              width: 60,
              height: 60,
              bgcolor: 'primary.main',
              mr: 3,
              boxShadow: 2,
              flexShrink: 0,
            }}
          >
            <BusinessIcon sx={{ fontSize: 30 }} />
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography
              variant="h4"
              gutterBottom
              sx={{
                fontWeight: 700,
                color: 'text.primary',
                mb: 0.5,
                lineHeight: 1.2,
              }}
            >
              {isEditing ? 'Edit Product' : 'Create New Product'}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.5 }}>
              {isEditing
                ? 'Update the product information below'
                : 'Fill in the details to create a new product'
              }
            </Typography>
          </Box>
        </Box>

        {loading && (
          <Box sx={{ mb: 3 }}>
            <LinearProgress
              sx={{
                borderRadius: 1,
                height: 6,
              }}
            />
          </Box>
        )}

        {error && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: 2,
              '& .MuiAlert-message': {
                fontSize: '1rem',
              },
            }}
          >
            {error}
          </Alert>
        )}

        <Divider sx={{ mb: 4 }} />

        <Box component="form" onSubmit={handleSubmit} noValidate>
          <Stack spacing={4}>
            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BusinessIcon sx={{ mr: 1.5, color: 'primary.main', fontSize: 20 }} />
                <Typography variant="h6" fontWeight={600} sx={{ color: 'text.primary' }}>
                  Product Name
                </Typography>
              </Box>
              <TextField
                fullWidth
                label="Product Name"
                placeholder="e.g., Core Platform, Mobile App, API Gateway"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={Boolean(validationErrors.name)}
                helperText={validationErrors.name || 'Enter a unique name for the product'}
                required
                disabled={loading}
                inputProps={{ maxLength: 100 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                    '&.Mui-focused': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderWidth: 2,
                      },
                    },
                  },
                  '& .MuiFormHelperText-root': {
                    marginLeft: 0,
                    marginTop: 1,
                  },
                }}
              />
            </Box>

            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DescriptionIcon sx={{ mr: 1.5, color: 'primary.main', fontSize: 20 }} />
                <Typography variant="h6" fontWeight={600} sx={{ color: 'text.primary' }}>
                  Description
                </Typography>
              </Box>
              <TextField
                fullWidth
                label="Description"
                placeholder="Describe the product's purpose and features..."
                value={formData.description}
                onChange={handleInputChange('description')}
                error={Boolean(validationErrors.description)}
                helperText={
                  validationErrors.description ||
                  `Optional description (${formData.description.length}/500 characters)`
                }
                multiline
                rows={5}
                disabled={loading}
                inputProps={{ maxLength: 500 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    alignItems: 'flex-start',
                    '&:hover': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                    '&.Mui-focused': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderWidth: 2,
                      },
                    },
                  },
                  '& .MuiFormHelperText-root': {
                    marginLeft: 0,
                    marginTop: 1,
                  },
                }}
              />
            </Box>
          </Stack>

          <Divider sx={{ my: 4 }} />

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            justifyContent="flex-end"
            alignItems="center"
            sx={{ pt: 2 }}
          >
            <Button
              variant="outlined"
              size="large"
              onClick={onCancel}
              disabled={loading}
              startIcon={<CancelIcon />}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 500,
                px: 3,
                py: 1.5,
                minWidth: { xs: '100%', sm: 'auto' },
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 2,
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              size="large"
              disabled={loading}
              startIcon={<SaveIcon />}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                minWidth: { xs: '100%', sm: 'auto' },
                boxShadow: 2,
                '&:hover': {
                  boxShadow: 4,
                  transform: 'translateY(-1px)',
                },
                '&:disabled': {
                  transform: 'none',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              {loading ? 'Saving...' : isEditing ? 'Update Product' : 'Create Product'}
            </Button>
          </Stack>
        </Box>
      </Paper>
    </Fade>
  );
};
