
import { Routes, Route } from 'react-router-dom';

import { ProductListPage } from '../ProductListPage';
import { ProductDetailPage } from '../ProductDetailPage';
import { ProductNewPage } from '../ProductNewPage';
import { ProductEditPage } from '../ProductEditPage';

/**
 * Router component for handling all product routes
 */
export const ProductRouter = () => {
  return (
    <Routes>
      <Route path="/" element={<ProductListPage />} />
      <Route path="/new" element={<ProductNewPage />} />
      <Route path="/:id" element={<ProductDetailPage />} />
      <Route path="/:id/edit" element={<ProductEditPage />} />
    </Routes>
  );
};
