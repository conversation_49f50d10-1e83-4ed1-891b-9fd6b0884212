import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Avatar,
  Divider,
  Stack,
  Slide,
} from '@mui/material';
import {
  Warning as WarningIcon,
  Delete as DeleteIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

const Transition = React.forwardRef(function Transition(props: any, ref: React.Ref<unknown>) {
  return <Slide direction="up" ref={ref} {...props} />;
});

import { Product } from '../../api';

interface DeleteConfirmationDialogProps {
  open: boolean;
  product: Product | null;
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * Confirmation dialog for deleting products
 */
export const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  open,
  product,
  onConfirm,
  onCancel,
}) => {
  if (!product) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="sm"
      fullWidth
      TransitionComponent={Transition}
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: 6,
        },
      }}
    >
      <DialogTitle sx={{ pb: 2 }}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar
            sx={{
              bgcolor: 'error.main',
              width: 48,
              height: 48,
            }}
          >
            <WarningIcon />
          </Avatar>
          <Box>
            <Typography variant="h5" fontWeight={700} color="text.primary">
              Delete Product
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This action cannot be undone
            </Typography>
          </Box>
        </Stack>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ py: 3 }}>
        <Stack spacing={2}>
          <Typography variant="body1" sx={{ fontSize: '1.1rem' }}>
            Are you sure you want to delete the product{' '}
            <Typography
              component="span"
              fontWeight={700}
              color="error.main"
              sx={{
                bgcolor: 'error.light',
                px: 1,
                py: 0.5,
                borderRadius: 1,
                fontSize: 'inherit',
              }}
            >
              "{product.name}"
            </Typography>
            ?
          </Typography>

          <Box
            sx={{
              p: 2,
              bgcolor: 'error.light',
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'error.main',
            }}
          >
            <Typography
              variant="body2"
              color="error.dark"
              sx={{ fontWeight: 500 }}
            >
              ⚠️ Warning: All data associated with this product will be permanently removed
              and cannot be recovered.
            </Typography>
          </Box>
        </Stack>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 3 }}>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          width="100%"
          justifyContent="flex-end"
          alignItems="center"
        >
          <Button
            onClick={onCancel}
            variant="outlined"
            size="large"
            startIcon={<CancelIcon />}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              minWidth: { xs: '100%', sm: 'auto' },
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: 2,
              },
              transition: 'all 0.2s ease-in-out',
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            color="error"
            variant="contained"
            size="large"
            startIcon={<DeleteIcon />}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 600,
              px: 3,
              minWidth: { xs: '100%', sm: 'auto' },
              boxShadow: 2,
              '&:hover': {
                boxShadow: 4,
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
            }}
          >
            Delete Product
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};
