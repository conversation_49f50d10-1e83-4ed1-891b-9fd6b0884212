import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardActions,
  Grid,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Fade,
  Skeleton,
  Avatar,
  Stack,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  Visibility as VisibilityIcon,
  AccessTime as AccessTimeIcon,
} from '@mui/icons-material';
import {
  Header,
  Page,
  Content,
  ContentHeader,
  SupportButton,
  Progress,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi } from '@backstage/core-plugin-api';

import { productApiRef, Product } from '../../api';
import { DeleteConfirmationDialog } from '../DeleteConfirmationDialog';

/**
 * Main product list page component
 */
export const ProductListPage = () => {
  const navigate = useNavigate();
  const productApi = useApi(productApiRef);

  // State management
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    product: Product | null;
  }>({ open: false, product: null });

  // Load products on component mount
  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await productApi.getProducts();
      setProducts(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      loadProducts();
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const data = await productApi.searchProducts(query);
      setProducts(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search products');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (product: Product) => {
    setDeleteDialog({ open: true, product });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.product) return;

    try {
      await productApi.deleteProduct(deleteDialog.product.id);
      setDeleteDialog({ open: false, product: null });
      loadProducts();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete product');
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialog({ open: false, product: null });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading && products.length === 0) {
    return (
      <Page themeId="tool">
        <Header title="Products" subtitle="Manage your organization's products">
          <SupportButton>Manage products in your organization</SupportButton>
        </Header>
        <Content>
          <ContentHeader title="All Products">
            <Skeleton variant="rectangular" width={140} height={36} />
          </ContentHeader>

          <Box sx={{ mb: 3 }}>
            <Skeleton variant="rectangular" width={400} height={56} />
          </Box>

          <Grid container spacing={3}>
            {[...Array(6)].map((_, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card sx={{ height: 280 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1 }} />
                      <Skeleton variant="text" width="60%" height={28} />
                    </Box>
                    <Skeleton variant="text" width="100%" height={20} />
                    <Skeleton variant="text" width="80%" height={20} />
                    <Skeleton variant="text" width="90%" height={20} />
                    <Box sx={{ mt: 2 }}>
                      <Skeleton variant="rectangular" width={120} height={24} />
                    </Box>
                  </CardContent>
                  <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                    <Skeleton variant="rectangular" width={80} height={32} />
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Skeleton variant="circular" width={32} height={32} />
                      <Skeleton variant="circular" width={32} height={32} />
                    </Box>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Content>
      </Page>
    );
  }

  if (error) {
    return (
      <Page themeId="tool">
        <Header title="Products" subtitle="Manage your organization's products">
          <SupportButton>Manage products in your organization</SupportButton>
        </Header>
        <Content>
          <ErrorPanel error={new Error(error)} />
        </Content>
      </Page>
    );
  }

  return (
    <Page themeId="tool">
      <Header title="Products" subtitle="Manage your organization's products">
        <SupportButton>Manage products in your organization</SupportButton>
      </Header>
      <Content>
        <ContentHeader title="All Products">
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/product/new')}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 600,
              px: 3,
              py: 1.5,
              boxShadow: 2,
              '&:hover': {
                boxShadow: 4,
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
            }}
          >
            Add Product
          </Button>
        </ContentHeader>

        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'center' }}>
          <TextField
            placeholder="Search products by name or description..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: 'text.secondary' }} />
                </InputAdornment>
              ),
            }}
            sx={{
              width: '100%',
              maxWidth: 600,
              '& .MuiOutlinedInput-root': {
                borderRadius: 3,
                backgroundColor: 'background.paper',
                '&:hover': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                  },
                },
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderWidth: 2,
                  },
                },
              },
            }}
          />
        </Box>

        {products.length === 0 ? (
          <Fade in timeout={600}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: 400,
                textAlign: 'center',
                py: 6,
              }}
            >
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  bgcolor: 'primary.main',
                  mb: 3,
                  boxShadow: 3,
                }}
              >
                <BusinessIcon sx={{ fontSize: 60 }} />
              </Avatar>
              <Typography variant="h4" color="text.primary" gutterBottom fontWeight={600}>
                {searchQuery ? 'No products found' : 'No products yet'}
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 400 }}>
                {searchQuery
                  ? 'Try adjusting your search criteria or create a new product'
                  : 'Get started by creating your first product to organize your offerings'}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                size="large"
                startIcon={<AddIcon />}
                onClick={() => navigate('/product/new')}
                sx={{
                  borderRadius: 3,
                  textTransform: 'none',
                  fontWeight: 600,
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  boxShadow: 3,
                  '&:hover': {
                    boxShadow: 6,
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.3s ease-in-out',
                }}
              >
                {searchQuery ? 'Create New Product' : 'Add Your First Product'}
              </Button>
            </Box>
          </Fade>
        ) : (
          <Grid container spacing={3} sx={{ alignItems: 'stretch' }}>
            {products.map((product, index) => (
              <Grid item xs={12} sm={6} lg={4} key={product.id}>
                <Fade in timeout={300 + index * 100}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: 3,
                      boxShadow: 2,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        boxShadow: 8,
                        transform: 'translateY(-4px)',
                      },
                      cursor: 'pointer',
                      minHeight: 320,
                    }}
                    onClick={() => navigate(`/product/${product.id}`)}
                  >
                    <CardContent sx={{ flexGrow: 1, p: 3, display: 'flex', flexDirection: 'column' }}>
                      <Stack spacing={2} sx={{ height: '100%' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              bgcolor: 'primary.main',
                              mr: 2,
                              flexShrink: 0,
                            }}
                          >
                            <BusinessIcon />
                          </Avatar>
                          <Typography
                            variant="h6"
                            component="h2"
                            sx={{
                              fontWeight: 600,
                              color: 'text.primary',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              lineHeight: 1.3,
                            }}
                          >
                            {product.name}
                          </Typography>
                        </Box>

                        <Divider />

                        <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                          {product.description ? (
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                lineHeight: 1.6,
                                flexGrow: 1,
                                minHeight: 72,
                              }}
                            >
                              {product.description}
                            </Typography>
                          ) : (
                            <Typography
                              variant="body2"
                              color="text.disabled"
                              sx={{
                                fontStyle: 'italic',
                                flexGrow: 1,
                                minHeight: 72,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                textAlign: 'center',
                              }}
                            >
                              No description provided
                            </Typography>
                          )}
                        </Box>

                        <Box sx={{ mt: 'auto', pt: 2, display: 'flex', justifyContent: 'center' }}>
                          <Chip
                            icon={<AccessTimeIcon />}
                            label={`Created ${formatDate(product.createdAt)}`}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderRadius: 2,
                              '& .MuiChip-icon': {
                                fontSize: 16,
                              },
                            }}
                          />
                        </Box>
                      </Stack>
                    </CardContent>

                    <CardActions sx={{
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      px: 3,
                      py: 2,
                      mt: 'auto',
                      borderTop: '1px solid',
                      borderColor: 'divider',
                    }}>
                      <Button
                        size="small"
                        startIcon={<VisibilityIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/product/${product.id}`);
                        }}
                        sx={{
                          textTransform: 'none',
                          fontWeight: 500,
                          borderRadius: 2,
                          px: 2,
                          py: 1,
                        }}
                      >
                        View Details
                      </Button>

                      <Stack direction="row" spacing={1} sx={{ alignItems: 'center' }}>
                        <Tooltip title="Edit Product" arrow>
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/product/${product.id}/edit`);
                            }}
                            sx={{
                              color: 'primary.main',
                              '&:hover': {
                                bgcolor: 'primary.light',
                                color: 'primary.contrastText',
                              },
                              transition: 'all 0.2s ease-in-out',
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Product" arrow>
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteClick(product);
                            }}
                            sx={{
                              color: 'error.main',
                              '&:hover': {
                                bgcolor: 'error.light',
                                color: 'error.contrastText',
                              },
                              transition: 'all 0.2s ease-in-out',
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </CardActions>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
        )}

        <DeleteConfirmationDialog
          open={deleteDialog.open}
          product={deleteDialog.product}
          onConfirm={handleDeleteConfirm}
          onCancel={handleDeleteCancel}
        />
      </Content>
    </Page>
  );
};
