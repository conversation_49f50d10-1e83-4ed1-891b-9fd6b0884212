import {
  createPlugin,
  createRoutableExtension,
  createApiFactory,
  discoveryApiRef,
  fetchApiRef,
} from '@backstage/core-plugin-api';

import { rootRouteRef } from './routes';
import { productApiRef, ProductClient } from './api';

/**
 * Product plugin definition
 */
export const productPlugin = createPlugin({
  id: 'product',
  apis: [
    createApiFactory({
      api: productApiRef,
      deps: { discoveryApi: discoveryApiRef, fetchApi: fetchApiRef },
      factory: ({ discoveryApi, fetchApi }) =>
        new ProductClient({ discoveryApi, fetchApi }),
    }),
  ],
  routes: {
    root: rootRouteRef,
  },
});

/**
 * Main product page component extension
 */
export const ProductPage = productPlugin.provide(
  createRoutableExtension({
    name: 'ProductPage',
    component: () =>
      import('./components/ProductRouter').then(m => m.ProductRouter),
    mountPoint: rootRouteRef,
  }),
);
