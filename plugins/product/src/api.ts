import {
  createApiR<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>tch<PERSON><PERSON>,
} from '@backstage/core-plugin-api';

/**
 * Product entity interface
 */
export interface Product {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create product input interface
 */
export interface CreateProductInput {
  name: string;
  description?: string;
}

/**
 * Update product input interface
 */
export interface UpdateProductInput {
  name?: string;
  description?: string;
}

/**
 * API response wrapper
 */
export interface ApiResponse<T> {
  data: T;
  message: string;
}

/**
 * Product API interface
 */
export interface ProductApi {
  /**
   * Get all products
   */
  getProducts(): Promise<Product[]>;

  /**
   * Get product by ID
   */
  getProductById(id: string): Promise<Product>;

  /**
   * Create a new product
   */
  createProduct(input: CreateProductInput): Promise<Product>;

  /**
   * Update an existing product
   */
  updateProduct(id: string, input: UpdateProductInput): Promise<Product>;

  /**
   * Delete a product
   */
  deleteProduct(id: string): Promise<void>;

  /**
   * Search products
   */
  searchProducts(query: string): Promise<Product[]>;
}

/**
 * API reference for dependency injection
 */
export const productApiRef = createApiRef<ProductApi>({
  id: 'plugin.product.service',
});

/**
 * Product API client implementation
 */
export class ProductClient implements ProductApi {
  private readonly discoveryApi: DiscoveryApi;
  private readonly fetchApi: FetchApi;

  constructor(options: { discoveryApi: DiscoveryApi; fetchApi: FetchApi }) {
    this.discoveryApi = options.discoveryApi;
    this.fetchApi = options.fetchApi;
  }

  private async getBaseUrl() {
    return `${await this.discoveryApi.getBaseUrl('product')}/products`;
  }

  async getProducts(): Promise<Product[]> {
    const url = await this.getBaseUrl();
    const response = await this.fetchApi.fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch products: ${response.statusText}`);
    }

    const result: ApiResponse<Product[]> = await response.json();
    return result.data;
  }

  async getProductById(id: string): Promise<Product> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url);

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error(`Product with ID '${id}' not found`);
      }
      throw new Error(`Failed to fetch product: ${response.statusText}`);
    }

    const result: ApiResponse<Product> = await response.json();
    return result.data;
  }

  async createProduct(input: CreateProductInput): Promise<Product> {
    const url = await this.getBaseUrl();
    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to create product: ${response.statusText}`);
    }

    const result: ApiResponse<Product> = await response.json();
    return result.data;
  }

  async updateProduct(id: string, input: UpdateProductInput): Promise<Product> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      if (response.status === 404) {
        throw new Error(`Product with ID '${id}' not found`);
      }
      throw new Error(errorData.message || `Failed to update product: ${response.statusText}`);
    }

    const result: ApiResponse<Product> = await response.json();
    return result.data;
  }

  async deleteProduct(id: string): Promise<void> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      if (response.status === 404) {
        throw new Error(`Product with ID '${id}' not found`);
      }
      throw new Error(errorData.message || `Failed to delete product: ${response.statusText}`);
    }
  }

  async searchProducts(query: string): Promise<Product[]> {
    const url = `${await this.getBaseUrl()}?search=${encodeURIComponent(query)}`;
    const response = await this.fetchApi.fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to search products: ${response.statusText}`);
    }

    const result: ApiResponse<Product[]> = await response.json();
    return result.data;
  }
}
