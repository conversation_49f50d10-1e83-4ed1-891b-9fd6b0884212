import { createRouteRef, createSubRouteRef } from '@backstage/core-plugin-api';

/**
 * Route references for the product plugin
 */

// Root route for the product plugin
export const rootRouteRef = createRouteRef({
  id: 'product',
});

// Sub-routes for specific product pages
export const productRouteRef = createSubRouteRef({
  id: 'product:detail',
  parent: rootRouteRef,
  path: '/:id',
});

export const newProductRouteRef = createSubRouteRef({
  id: 'product:new',
  parent: rootRouteRef,
  path: '/new',
});

export const editProductRouteRef = createSubRouteRef({
  id: 'product:edit',
  parent: rootRouteRef,
  path: '/:id/edit',
});
