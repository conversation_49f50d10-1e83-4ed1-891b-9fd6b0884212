{"name": "@internal/plugin-postman-converter", "version": "0.1.0", "license": "Apache-2.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.esm.js", "types": "dist/index.d.ts"}, "backstage": {"role": "frontend-plugin"}, "sideEffects": false, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/core-components": "^0.17.1", "@backstage/core-plugin-api": "^1.10.6", "@backstage/theme": "^0.6.5", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.0", "@mui/lab": "^5.0.0-alpha.150", "@mui/material": "^5.15.0", "@mui/styles": "^5.15.0", "chai": "^5.2.0", "react-use": "^17.2.4"}, "peerDependencies": {"react": "^16.13.1 || ^17.0.0 || ^18.0.0", "react-router-dom": "^6.3.0"}, "devDependencies": {"@backstage/cli": "^0.32.0", "@backstage/core-app-api": "^1.16.1", "@backstage/dev-utils": "^1.1.9", "@backstage/test-utils": "^1.7.7", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/chai": "^5.2.2", "msw": "^2.0.0", "react": "^16.13.1 || ^17.0.0 || ^18.0.0"}, "files": ["dist"]}