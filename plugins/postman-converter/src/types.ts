// Re-export shared types
export * from './types/shared';
export * from './types/apiTesting';
export * from './types/k6Converter';

// Core collection types (keep these here for backward compatibility)
export interface Collection {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
  content?: string; // Only included in detailed view
  version?: number; // Only included in detailed view
}

export interface CollectionVersion {
  id: string;
  collection_id: string;
  version: number;
  content: string;
  created_at: string;
  user_id: string;
}

export interface CollectionCreateRequest {
  name: string;
  description: string;
  content: string;
}

export interface CollectionUpdateRequest {
  name?: string;
  description?: string;
  content?: string;
}
