import { Collection } from '../types';

/**
 * K6 script generation options
 */
export interface K6GenerationOptions {
  baseUrl?: string;
  includeHeaders: boolean;
  includeAuth: boolean;
  includeTests: boolean;
  splitFiles: boolean;
  outputFormat: 'single' | 'split';
  loadProfile: K6LoadProfile;
}

/**
 * K6 load testing profiles
 */
export type K6LoadProfile = 'smoke' | 'load' | 'stress' | 'spike' | 'volume' | 'custom';

/**
 * K6 load configuration
 */
export interface K6LoadConfig {
  vus: number; // Virtual users
  duration: string; // Duration (e.g., '30s', '5m')
  rampUp?: string; // Ramp-up time
  rampDown?: string; // Ramp-down time
  stages?: K6Stage[];
}

/**
 * K6 execution stage
 */
export interface K6Stage {
  duration: string;
  target: number;
}

/**
 * K6 script file types
 */
export type K6ScriptFileType = 'actions' | 'scenarios' | 'config' | 'types' | 'main';

/**
 * K6 generated script file
 */
export interface K6ScriptFile {
  type: K6ScriptFileType;
  filename: string;
  content: string;
  language: 'javascript' | 'typescript';
}

/**
 * K6 script generation result
 */
export interface K6ScriptGenerationResult {
  files: K6ScriptFile[];
  metadata: K6ScriptMetadata;
  warnings: string[];
  errors: string[];
}

/**
 * K6 script metadata
 */
export interface K6ScriptMetadata {
  generatedAt: string;
  sourceCollection: {
    id: string;
    name: string;
    version?: number;
  };
  options: K6GenerationOptions;
  statistics: K6GenerationStatistics;
}

/**
 * K6 generation statistics
 */
export interface K6GenerationStatistics {
  totalRequests: number;
  totalFolders: number;
  convertedRequests: number;
  skippedRequests: number;
  generatedFiles: number;
  linesOfCode: number;
}

/**
 * K6 converter state
 */
export interface K6ConverterState {
  selectedCollection?: Collection;
  selectedFolders: string[];
  selectedRequests: string[];
  generationOptions: K6GenerationOptions;
  generatedScripts?: K6ScriptGenerationResult;
  isGenerating: boolean;
  error?: string;
}

/**
 * K6 converter handlers
 */
export interface K6ConverterHandlers {
  onCollectionSelect: (collection: Collection) => void;
  onFolderToggle: (folderId: string) => void;
  onRequestToggle: (requestId: string) => void;
  onOptionsChange: (options: Partial<K6GenerationOptions>) => void;
  onGenerate: () => Promise<void>;
  onDownload: (fileType: K6ScriptFileType) => void;
  onDownloadAll: () => void;
  onCopyToClipboard: (content: string) => void;
}

/**
 * Collection tree node for K6 converter
 */
export interface K6CollectionTreeNode {
  id: string;
  name: string;
  type: 'collection' | 'folder' | 'request';
  method?: string; // For requests
  children?: K6CollectionTreeNode[];
  selected: boolean;
  indeterminate?: boolean; // For folders with partially selected children
  parentId?: string;
}

/**
 * K6 converter configuration panel props
 */
export interface K6ConfigurationPanelProps {
  options: K6GenerationOptions;
  onChange: (options: Partial<K6GenerationOptions>) => void;
  disabled?: boolean;
}

/**
 * K6 script output panel props
 */
export interface K6ScriptOutputPanelProps {
  scripts?: K6ScriptGenerationResult;
  loading: boolean;
  error?: string;
  onDownload: (fileType: K6ScriptFileType) => void;
  onDownloadAll: () => void;
  onCopyToClipboard: (content: string) => void;
}

/**
 * K6 folder selector props
 */
export interface K6FolderSelectorProps {
  collection?: Collection;
  selectedFolders: string[];
  selectedRequests: string[];
  onFolderToggle: (folderId: string) => void;
  onRequestToggle: (requestId: string) => void;
}

/**
 * K6 load profile configurations
 */
export const K6_LOAD_PROFILES: Record<K6LoadProfile, K6LoadConfig> = {
  smoke: {
    vus: 1,
    duration: '30s',
  },
  load: {
    vus: 10,
    duration: '5m',
    stages: [
      { duration: '30s', target: 10 },
      { duration: '4m', target: 10 },
      { duration: '30s', target: 0 },
    ],
  },
  stress: {
    vus: 50,
    duration: '10m',
    stages: [
      { duration: '2m', target: 10 },
      { duration: '5m', target: 50 },
      { duration: '2m', target: 100 },
      { duration: '1m', target: 0 },
    ],
  },
  spike: {
    vus: 100,
    duration: '5m',
    stages: [
      { duration: '10s', target: 100 },
      { duration: '1m', target: 100 },
      { duration: '10s', target: 1400 },
      { duration: '3m', target: 1400 },
      { duration: '10s', target: 100 },
      { duration: '1m', target: 100 },
      { duration: '10s', target: 0 },
    ],
  },
  volume: {
    vus: 200,
    duration: '30m',
  },
  custom: {
    vus: 10,
    duration: '5m',
  },
};
