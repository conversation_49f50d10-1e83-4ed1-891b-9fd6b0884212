import { FormField } from './shared';

/**
 * HTTP method types
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

/**
 * Authentication types
 */
export type AuthType = 'none' | 'basic' | 'bearer' | 'apiKey';

/**
 * Request body modes
 */
export type BodyMode = 'none' | 'raw' | 'form-data' | 'urlencoded';

/**
 * Test result interface
 */
export interface TestResult {
  id: string;
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

/**
 * Basic authentication configuration
 */
export interface BasicAuth {
  username: string;
  password: string;
}

/**
 * Bearer token authentication configuration
 */
export interface BearerAuth {
  token: string;
}

/**
 * API key authentication configuration
 */
export interface ApiKeyAuth {
  key: string;
  value: string;
  in: 'header' | 'query';
}

/**
 * Authentication configuration
 */
export interface AuthConfig {
  type: AuthType;
  basic?: BasicAuth;
  bearer?: BearerAuth;
  apiKey?: ApiKeyAuth;
}

/**
 * Form data field
 */
export interface FormDataField {
  key: string;
  value: string;
  type: string;
}

/**
 * URL encoded field
 */
export interface UrlEncodedField {
  key: string;
  value: string;
}

/**
 * Request body configuration
 */
export interface RequestBody {
  mode: BodyMode;
  raw?: string;
  formData?: FormDataField[];
  urlencoded?: UrlEncodedField[];
  enabled?: boolean;
}

/**
 * Test script metadata
 */
export interface TestScriptMetadata {
  isAutoGenerated: boolean;
  hasUnsavedAutoGenerated: boolean;
  lastAutoGeneratedAt?: string;
}

/**
 * API request interface
 */
export interface ApiRequest {
  id: string;
  name: string;
  method: HttpMethod;
  url: string;
  headers: FormField[];
  params: FormField[];
  body: RequestBody;
  auth?: AuthConfig;
  preRequestScript?: string;
  testScript?: string;
  lastTestResults?: TestResult[];
  testScriptMetadata?: TestScriptMetadata;
}

/**
 * API response interface
 */
export interface ApiResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: string;
  time: number; // in milliseconds
  size: number;
  error?: string;
  url?: string;
}

/**
 * Environment variable interface
 */
export interface EnvironmentVariable extends FormField {
  // Inherits key, value, enabled from FormField
}

/**
 * API environment interface
 */
export interface ApiEnvironment {
  id: string;
  name: string;
  variables: EnvironmentVariable[];
}

/**
 * API folder interface
 */
export interface ApiFolder {
  id: string;
  name: string;
  parentId?: string;
  requests: string[]; // Array of request IDs
  folders: ApiFolder[]; // Array of subfolders
}

/**
 * API collection interface
 */
export interface ApiCollection {
  id: string;
  name: string;
  description: string;
  folders: ApiFolder[];
  requests: Record<string, ApiRequest>;
  environments: ApiEnvironment[];
  _orphanedRequests?: string[]; // For debugging - requests that couldn't be added to folders
}

/**
 * Request execution context
 */
export interface RequestExecutionContext {
  request: ApiRequest;
  environment?: ApiEnvironment;
  collection?: ApiCollection;
}

/**
 * Request execution result
 */
export interface RequestExecutionResult {
  response: ApiResponse;
  testResults: TestResult[];
  executionTime: number;
  error?: string;
}

/**
 * Collection import/export options
 */
export interface CollectionImportOptions {
  mergeStrategy: 'replace' | 'merge' | 'append';
  preserveIds: boolean;
  validateSchema: boolean;
}

/**
 * Collection export options
 */
export interface CollectionExportOptions {
  format: 'postman' | 'openapi' | 'insomnia';
  includeEnvironments: boolean;
  includeTests: boolean;
  minify: boolean;
}
