import React from 'react';

/**
 * Common UI component props
 */
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

/**
 * Loading state interface
 */
export interface LoadingState {
  loading: boolean;
  error?: Error | string;
}

/**
 * Async operation state
 */
export interface AsyncState<T> extends LoadingState {
  data?: T;
}

/**
 * Form field interface
 */
export interface FormField {
  key: string;
  value: string;
  enabled: boolean;
}

/**
 * Dialog component props
 */
export interface DialogProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
}

/**
 * Confirmation dialog props
 */
export interface ConfirmationDialogProps extends DialogProps {
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void | Promise<void>;
  severity?: 'info' | 'warning' | 'error' | 'success';
}

/**
 * Tab panel props
 */
export interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  id?: string;
}

/**
 * Menu item interface
 */
export interface MenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
  divider?: boolean;
}

/**
 * Context menu props
 */
export interface ContextMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  items: MenuItem[];
}

/**
 * Notification interface
 */
export interface Notification {
  id: string;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
  autoHideDuration?: number;
}

/**
 * File upload interface
 */
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

/**
 * Search and filter interface
 */
export interface SearchFilter {
  query: string;
  filters: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Pagination interface
 */
export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

/**
 * Table column interface
 */
export interface TableColumn<T = any> {
  id: string;
  label: string;
  field?: keyof T;
  sortable?: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: T) => React.ReactNode;
}

/**
 * Table props interface
 */
export interface TableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: Pagination;
  onSort?: (field: string, order: 'asc' | 'desc') => void;
  onPageChange?: (page: number) => void;
  onRowClick?: (row: T) => void;
}

/**
 * Theme mode type
 */
export type ThemeMode = 'light' | 'dark' | 'auto';

/**
 * Component size variants
 */
export type ComponentSize = 'small' | 'medium' | 'large';

/**
 * Component color variants
 */
export type ComponentColor = 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';

/**
 * HTTP status codes
 */
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
}

/**
 * API response wrapper
 */
export interface ApiResponseWrapper<T = any> {
  data: T;
  message?: string;
  status: HttpStatusCode;
  timestamp: string;
}

/**
 * API error response
 */
export interface ApiError {
  message: string;
  code: string;
  status: HttpStatusCode;
  details?: Record<string, any>;
}
