import {
  createPlugin,
  createRoutableExtension,
  createApiFactory,
  discoveryApiRef,
  fetchApiRef,
} from '@backstage/core-plugin-api';

import { rootRouteRef, collectionRouteRef, newCollectionRouteRef, editCollectionRouteRef } from './routes';
import { postmanConverterApiRef, PostmanConverterClient } from './api';

export const postmanConverterPlugin = createPlugin({
  id: 'postman-converter',
  apis: [
    createApiFactory({
      api: postmanConverterApiRef,
      deps: { discoveryApi: discoveryApiRef, fetchApi: fetchApiRef },
      factory: ({ discoveryApi, fetchApi }) =>
        new PostmanConverterClient({ discoveryApi, fetchApi }),
    }),
  ],
  routes: {
    root: rootRouteRef,
    collection: collectionRouteRef,
    newCollection: newCollectionRouteRef,
    editCollection: editCollectionRouteRef,
  },
});

export const PostmanConverterPage = postmanConverterPlugin.provide(
  createRoutableExtension({
    name: 'PostmanConverterPage',
    component: () =>
      import('./components/TabsPage').then(m => m.TabsPage),
    mountPoint: rootRouteRef,
  }),
);

export const PostmanConverterCollectionPage = postmanConverterPlugin.provide(
  createRoutableExtension({
    name: 'PostmanConverterCollectionPage',
    component: () =>
      import('./components/CollectionDetailPage').then(m => m.CollectionDetailPage),
    mountPoint: collectionRouteRef,
  }),
);

export const PostmanConverterNewCollectionPage = postmanConverterPlugin.provide(
  createRoutableExtension({
    name: 'PostmanConverterNewCollectionPage',
    component: () =>
      import('./components/CollectionFormPage').then(m => m.CollectionFormPage),
    mountPoint: newCollectionRouteRef,
  }),
);

export const PostmanConverterEditCollectionPage = postmanConverterPlugin.provide(
  createRoutableExtension({
    name: 'PostmanConverterEditCollectionPage',
    component: () =>
      import('./components/CollectionEditPage').then(m => m.CollectionEditPage),
    mountPoint: editCollectionRouteRef,
  }),
);
