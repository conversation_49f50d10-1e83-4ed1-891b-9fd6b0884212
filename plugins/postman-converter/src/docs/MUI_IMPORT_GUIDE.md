# MUI Import Enhancement Guide

This guide provides strategies for improving MUI imports in the Postman Converter package while maintaining ESLint compliance with the `@mui/material` barrel import restrictions.

## Current ESLint Rule

The codebase enforces specific path imports:
```
'@mui/material' import is restricted from being used. Please import '@mui/material/...' instead.
```

## Enhanced Import Strategies

### 1. Organized Individual Imports (Recommended)

Group imports by component type with clear comments:

```typescript
// MUI Core Components
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// MUI Form Components  
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Select from '@mui/material/Select';

// MUI Data Display
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';

// MUI Feedback
import Alert from '@mui/material/Alert';
import CircularProgress from '@mui/material/CircularProgress';

// MUI Styles
import { useTheme, styled } from '@mui/material/styles';
```

### 2. Component Bundle Utilities

Use the `muiComponents.ts` utility for component bundles:

```typescript
import { 
  TableComponents, 
  DialogComponents, 
  FormComponents 
} from '../../utils/muiComponents';

// Destructure what you need
const { Table, TableBody, TableCell } = TableComponents;
const { Dialog, DialogTitle, DialogContent } = DialogComponents;
```

### 3. Theme and Style Utilities

Enhanced theme utilities:

```typescript
import { 
  useTheme, 
  createThemeUtils, 
  sxPatterns,
  httpMethodColors 
} from '../../utils/muiComponents';

const theme = useTheme();
const themeUtils = createThemeUtils(theme);

// Use predefined sx patterns
<Box sx={sxPatterns.flexBetween}>
  <Typography>Title</Typography>
  <Button>Action</Button>
</Box>

// Use HTTP method colors
<Chip 
  label="GET" 
  sx={{ 
    backgroundColor: httpMethodColors.GET,
    color: 'white' 
  }} 
/>
```

## Import Organization Best Practices

### 1. Import Order

Follow this order for better readability:

```typescript
// 1. React and core libraries
import React, { useState } from 'react';

// 2. Backstage components
import { Content, ErrorPanel } from '@backstage/core-components';

// 3. MUI components (grouped by type)
// MUI Core Components
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';

// MUI Form Components
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';

// MUI Styles
import { useTheme } from '@mui/material/styles';

// 4. Third-party libraries
import { useAsync } from 'react-use';

// 5. Internal imports
import { postmanConverterApiRef } from '../../api';
import { Collection } from '../../types';
```

### 2. Component Grouping

Group related MUI components together:

```typescript
// Layout & Structure
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Stack from '@mui/material/Stack';

// Typography & Content
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';

// Interactive Elements
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Fab from '@mui/material/Fab';

// Form Controls
import TextField from '@mui/material/TextField';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Checkbox from '@mui/material/Checkbox';

// Data Display
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import Chip from '@mui/material/Chip';
import Avatar from '@mui/material/Avatar';

// Feedback & Status
import Alert from '@mui/material/Alert';
import CircularProgress from '@mui/material/CircularProgress';
import Snackbar from '@mui/material/Snackbar';

// Overlays & Dialogs
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import Modal from '@mui/material/Modal';
import Popover from '@mui/material/Popover';
```

### 3. Alias Patterns

For frequently used components, consider shorter aliases:

```typescript
// Common aliases
import { 
  Typography as Text,
  IconButton as IButton,
  CircularProgress as Spinner,
} from '../../utils/muiComponents';
```

## Performance Considerations

### 1. Tree Shaking

Individual imports ensure optimal tree shaking:

```typescript
// ✅ Good - Only imports what's needed
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';

// ❌ Avoid - Barrel imports (ESLint restricted)
import { Button, TextField } from '@mui/material';
```

### 2. Bundle Size

Monitor bundle size impact:

```bash
# Analyze bundle size
npm run build:analyze

# Check specific component impact
npx webpack-bundle-analyzer build/static/js/*.js
```

## Common Patterns

### 1. Form Components

```typescript
// Form-heavy components
import TextField from '@mui/material/TextField';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import InputLabel from '@mui/material/InputLabel';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
```

### 2. Data Display Components

```typescript
// Table-heavy components
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TablePagination from '@mui/material/TablePagination';
import TableSortLabel from '@mui/material/TableSortLabel';
```

### 3. Dialog Components

```typescript
// Dialog-heavy components
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
```

## Migration Strategy

### Phase 1: Organize Existing Imports
1. Group imports by component type
2. Add descriptive comments
3. Maintain alphabetical order within groups

### Phase 2: Implement Utilities
1. Use `muiComponents.ts` for component bundles
2. Implement theme utilities
3. Add common sx patterns

### Phase 3: Optimize Performance
1. Remove unused imports
2. Analyze bundle impact
3. Implement lazy loading where appropriate

## Tools and Utilities

### 1. VS Code Extensions
- **Auto Import - ES6, TS, JSX, TSX**: Automatic import management
- **TypeScript Importer**: Better import suggestions
- **Bracket Pair Colorizer**: Better code readability

### 2. ESLint Rules
```json
{
  "rules": {
    "import/order": ["error", {
      "groups": [
        "builtin",
        "external", 
        "internal",
        "parent",
        "sibling",
        "index"
      ],
      "newlines-between": "always"
    }]
  }
}
```

### 3. Custom Snippets
Create VS Code snippets for common import patterns:

```json
{
  "MUI Table Imports": {
    "prefix": "mui-table",
    "body": [
      "import Table from '@mui/material/Table';",
      "import TableBody from '@mui/material/TableBody';",
      "import TableCell from '@mui/material/TableCell';",
      "import TableContainer from '@mui/material/TableContainer';",
      "import TableHead from '@mui/material/TableHead';",
      "import TableRow from '@mui/material/TableRow';"
    ]
  }
}
```

## Benefits

1. **Better Organization**: Clear grouping and commenting
2. **Improved Performance**: Optimal tree shaking
3. **Enhanced Readability**: Logical import structure
4. **Easier Maintenance**: Consistent patterns
5. **ESLint Compliance**: Follows project rules
6. **Developer Experience**: Better IntelliSense and autocomplete
