import { Theme } from '@mui/material/styles';

/**
 * Enhanced theme utilities for consistent styling across the Postman Converter plugin
 */

export const getEnhancedShadows = (theme: Theme) => ({
  card: '0 4px 20px rgba(0, 0, 0, 0.08)',
  cardHover: '0 6px 24px rgba(0, 0, 0, 0.12)',
  cardElevated: '0 8px 32px rgba(0, 0, 0, 0.12)',
  button: '0 4px 12px rgba(0, 0, 0, 0.1)',
  buttonHover: '0 6px 16px rgba(0, 0, 0, 0.15)',
  panel: '0 2px 12px rgba(0, 0, 0, 0.04)',
  form: '0 1px 8px rgba(0, 0, 0, 0.06)',
  input: '0 1px 4px rgba(0, 0, 0, 0.04)',
});

export const getEnhancedBorderRadius = (theme: Theme) => ({
  xs: theme.spacing(0.5),
  small: theme.spacing(1),
  medium: theme.spacing(2),
  large: theme.spacing(3),
  xl: theme.spacing(4),
  round: '50%',
});

export const getEnhancedTransitions = () => ({
  smooth: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  fast: 'all 0.2s ease',
  slow: 'all 0.5s ease',
  bounce: 'all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
});

export const getEnhancedGradients = (theme: Theme) => ({
  primary: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  primaryLight: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}10 100%)`,
  primarySubtle: `linear-gradient(135deg, ${theme.palette.primary.main}08 0%, ${theme.palette.secondary.main}05 100%)`,
  background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
  backgroundElevated: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, rgba(255, 255, 255, 0.8) 100%)`,
  radialPrimary: `radial-gradient(circle at 50% 50%, ${theme.palette.primary.main}05 0%, transparent 70%)`,
  radialAccent: `radial-gradient(circle at 20% 80%, ${theme.palette.primary.main}20 0%, transparent 50%), radial-gradient(circle at 80% 20%, ${theme.palette.secondary.main}15 0%, transparent 50%)`,
  glass: `linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)`,
});

export const getEnhancedSpacing = (theme: Theme) => ({
  xs: theme.spacing(0.5),
  sm: theme.spacing(1),
  md: theme.spacing(2),
  lg: theme.spacing(3),
  xl: theme.spacing(4),
  xxl: theme.spacing(6),
  xxxl: theme.spacing(8),
});

export const getEnhancedColors = (theme: Theme) => ({
  success: {
    light: theme.palette.success.light,
    main: theme.palette.success.main,
    dark: theme.palette.success.dark,
    subtle: `${theme.palette.success.main}15`,
  },
  warning: {
    light: theme.palette.warning.light,
    main: theme.palette.warning.main,
    dark: theme.palette.warning.dark,
    subtle: `${theme.palette.warning.main}15`,
  },
  error: {
    light: theme.palette.error.light,
    main: theme.palette.error.main,
    dark: theme.palette.error.dark,
    subtle: `${theme.palette.error.main}15`,
  },
  info: {
    light: theme.palette.info.light,
    main: theme.palette.info.main,
    dark: theme.palette.info.dark,
    subtle: `${theme.palette.info.main}15`,
  },
});

/**
 * Common styled component patterns
 */
export const getCardStyles = (theme: Theme) => ({
  borderRadius: getEnhancedBorderRadius(theme).medium,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: getEnhancedShadows(theme).card,
  overflow: 'hidden' as const,
  transition: getEnhancedTransitions().smooth,
  '&:hover': {
    boxShadow: getEnhancedShadows(theme).cardHover,
    transform: 'translateY(-2px)',
  },
});

export const getElevatedCardStyles = (theme: Theme) => ({
  borderRadius: getEnhancedBorderRadius(theme).large,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: getEnhancedShadows(theme).cardElevated,
  background: getEnhancedGradients(theme).backgroundElevated,
  overflow: 'hidden' as const,
  position: 'relative' as const,
  '&::before': {
    content: '""',
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: getEnhancedGradients(theme).primary,
  },
});

export const getButtonStyles = (theme: Theme) => ({
  borderRadius: getEnhancedBorderRadius(theme).large,
  textTransform: 'none' as const,
  fontWeight: theme.typography.fontWeightMedium,
  boxShadow: getEnhancedShadows(theme).button,
  transition: getEnhancedTransitions().smooth,
  padding: theme.spacing(1.5, 3),
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: getEnhancedShadows(theme).buttonHover,
  },
});

export const getFormFieldStyles = (theme: Theme) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: getEnhancedBorderRadius(theme).medium,
    transition: getEnhancedTransitions().smooth,
    '&:hover': {
      boxShadow: getEnhancedShadows(theme).input,
    },
    '&.Mui-focused': {
      boxShadow: `0 0 0 2px ${theme.palette.primary.main}25`,
    },
  },
  '& .MuiInputLabel-root': {
    fontWeight: theme.typography.fontWeightMedium,
  },
});

export const getHeaderStyles = (theme: Theme) => ({
  background: getEnhancedGradients(theme).primaryLight,
  borderRadius: getEnhancedBorderRadius(theme).large,
  border: `1px solid ${theme.palette.divider}`,
  position: 'relative' as const,
  overflow: 'hidden' as const,
  '&::before': {
    content: '""',
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: getEnhancedGradients(theme).radialAccent,
    pointerEvents: 'none' as const,
  },
});

export const getContainerStyles = (theme: Theme) => ({
  maxWidth: '1200px',
  margin: '0 auto',
  padding: theme.spacing(0, 2),
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(0, 3),
  },
  [theme.breakpoints.up('lg')]: {
    padding: theme.spacing(0, 4),
  },
});

export const getResponsiveSpacing = (theme: Theme) => ({
  xs: {
    padding: theme.spacing(2),
    margin: theme.spacing(2, 0),
  },
  sm: {
    padding: theme.spacing(3),
    margin: theme.spacing(3, 0),
  },
  md: {
    padding: theme.spacing(4),
    margin: theme.spacing(4, 0),
  },
  lg: {
    padding: theme.spacing(6),
    margin: theme.spacing(6, 0),
  },
});

export const getLoadingStyles = (theme: Theme) => ({
  background: getEnhancedGradients(theme).background,
  borderRadius: getEnhancedBorderRadius(theme).medium,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: getEnhancedShadows(theme).panel,
});

export const getEmptyStateStyles = (theme: Theme) => ({
  background: getEnhancedGradients(theme).background,
  borderRadius: getEnhancedBorderRadius(theme).medium,
  border: `1px solid ${theme.palette.divider}`,
  position: 'relative' as const,
  overflow: 'hidden' as const,
  '&::before': {
    content: '""',
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: getEnhancedGradients(theme).radialPrimary,
    pointerEvents: 'none' as const,
  },
});
