/**
 * Centralized styles export for the Postman Converter package
 */

// Export all mixins and utilities
export * from './mixins';

// Re-export theme utilities for convenience
export * from '../theme';

// Common style constants
export const STYLE_CONSTANTS = {
  // Spacing values in pixels
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  
  // Border radius values
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
    xl: '16px',
    round: '50%',
  },
  
  // Font sizes in pixels
  fontSize: {
    xs: '10px',
    sm: '12px',
    md: '14px',
    lg: '16px',
    xl: '18px',
    xxl: '20px',
    h6: '20px',
    h5: '24px',
    h4: '28px',
    h3: '32px',
    h2: '40px',
    h1: '48px',
  },
  
  // Z-index values
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modalBackdrop: 1040,
    modal: 1050,
    popover: 1060,
    tooltip: 1070,
  },
  
  // Animation durations
  animation: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  
  // Breakpoints (matching Material-UI defaults)
  breakpoints: {
    xs: '0px',
    sm: '600px',
    md: '900px',
    lg: '1200px',
    xl: '1536px',
  },
} as const;

// HTTP method colors for consistent styling
export const HTTP_METHOD_COLORS = {
  GET: '#4CAF50',
  POST: '#2196F3',
  PUT: '#FF9800',
  DELETE: '#F44336',
  PATCH: '#9C27B0',
  HEAD: '#607D8B',
  OPTIONS: '#795548',
} as const;

// Status code colors
export const STATUS_CODE_COLORS = {
  success: '#4CAF50', // 2xx
  redirect: '#FF9800', // 3xx
  clientError: '#F44336', // 4xx
  serverError: '#9C27B0', // 5xx
  info: '#2196F3', // 1xx
} as const;

// Common CSS-in-JS style objects
export const commonStyles = {
  // Flex layouts
  flexCenter: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  flexBetween: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  flexColumn: {
    display: 'flex',
    flexDirection: 'column' as const,
  },
  
  // Text utilities
  textEllipsis: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap' as const,
  },
  
  // Positioning
  fullWidth: {
    width: '100%',
  },
  
  fullHeight: {
    height: '100%',
  },
  
  // Visibility
  visuallyHidden: {
    position: 'absolute' as const,
    width: '1px',
    height: '1px',
    padding: 0,
    margin: '-1px',
    overflow: 'hidden',
    clip: 'rect(0, 0, 0, 0)',
    whiteSpace: 'nowrap' as const,
    border: 0,
  },
} as const;
