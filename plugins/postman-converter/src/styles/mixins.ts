import { Theme } from '@mui/material/styles';
import { CSSObject } from '@mui/material/styles';

/**
 * Common style mixins for consistent styling across components
 */

/**
 * Flexbox utilities
 */
export const flexMixins = {
  center: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerVertical: {
    display: 'flex',
    alignItems: 'center',
  },
  centerHorizontal: {
    display: 'flex',
    justifyContent: 'center',
  },
  spaceBetween: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  column: {
    display: 'flex',
    flexDirection: 'column' as const,
  },
  columnCenter: {
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
  },
  wrap: {
    display: 'flex',
    flexWrap: 'wrap' as const,
  },
};

/**
 * Text utilities
 */
export const textMixins = {
  ellipsis: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap' as const,
  },
  multilineEllipsis: (lines: number) => ({
    display: '-webkit-box',
    WebkitLineClamp: lines,
    WebkitBoxOrient: 'vertical' as const,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  }),
  noSelect: {
    userSelect: 'none' as const,
    WebkitUserSelect: 'none' as const,
    MozUserSelect: 'none' as const,
    msUserSelect: 'none' as const,
  },
  breakWord: {
    wordBreak: 'break-word' as const,
    overflowWrap: 'break-word' as const,
  },
};

/**
 * Transition utilities
 */
export const transitionMixins = {
  smooth: {
    transition: 'all 0.3s ease',
  },
  fast: {
    transition: 'all 0.15s ease',
  },
  slow: {
    transition: 'all 0.5s ease',
  },
  hover: (transform = 'translateY(-2px)') => ({
    transition: 'all 0.3s ease',
    '&:hover': {
      transform,
    },
  }),
};

/**
 * Shadow utilities
 */
export const shadowMixins = (theme: Theme) => ({
  subtle: {
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  },
  medium: {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  },
  strong: {
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
  },
  inset: {
    boxShadow: 'inset 0 1px 3px rgba(0, 0, 0, 0.1)',
  },
});

/**
 * Border utilities
 */
export const borderMixins = (theme: Theme) => ({
  default: {
    border: `1px solid ${theme.palette.divider}`,
  },
  primary: {
    border: `1px solid ${theme.palette.primary.main}`,
  },
  secondary: {
    border: `1px solid ${theme.palette.secondary.main}`,
  },
  error: {
    border: `1px solid ${theme.palette.error.main}`,
  },
  success: {
    border: `1px solid ${theme.palette.success.main}`,
  },
  rounded: (radius = '8px') => ({
    borderRadius: radius,
  }),
  circle: {
    borderRadius: '50%',
  },
});

/**
 * Positioning utilities
 */
export const positionMixins = {
  absolute: {
    position: 'absolute' as const,
  },
  relative: {
    position: 'relative' as const,
  },
  fixed: {
    position: 'fixed' as const,
  },
  sticky: {
    position: 'sticky' as const,
  },
  fullSize: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  center: {
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
  },
};

/**
 * Scrollbar utilities
 */
export const scrollbarMixins = (theme: Theme) => ({
  thin: {
    '&::-webkit-scrollbar': {
      width: '6px',
      height: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: theme.palette.background.default,
    },
    '&::-webkit-scrollbar-thumb': {
      background: theme.palette.divider,
      borderRadius: '3px',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: theme.palette.text.secondary,
    },
  },
  hidden: {
    scrollbarWidth: 'none' as const,
    msOverflowStyle: 'none' as const,
    '&::-webkit-scrollbar': {
      display: 'none',
    },
  },
});

/**
 * Focus utilities
 */
export const focusMixins = (theme: Theme) => ({
  outline: {
    '&:focus': {
      outline: `2px solid ${theme.palette.primary.main}`,
      outlineOffset: '2px',
    },
  },
  ring: {
    '&:focus': {
      boxShadow: `0 0 0 3px ${theme.palette.primary.main}25`,
    },
  },
  visible: {
    '&:focus-visible': {
      outline: `2px solid ${theme.palette.primary.main}`,
      outlineOffset: '2px',
    },
  },
});

/**
 * Animation utilities
 */
export const animationMixins = {
  fadeIn: {
    '@keyframes fadeIn': {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
    animation: 'fadeIn 0.3s ease-in-out',
  },
  slideIn: {
    '@keyframes slideIn': {
      from: { transform: 'translateY(-10px)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
    animation: 'slideIn 0.3s ease-out',
  },
  pulse: {
    '@keyframes pulse': {
      '0%': { transform: 'scale(1)' },
      '50%': { transform: 'scale(1.05)' },
      '100%': { transform: 'scale(1)' },
    },
    animation: 'pulse 2s infinite',
  },
  spin: {
    '@keyframes spin': {
      from: { transform: 'rotate(0deg)' },
      to: { transform: 'rotate(360deg)' },
    },
    animation: 'spin 1s linear infinite',
  },
};

/**
 * Responsive utilities
 */
export const responsiveMixins = (theme: Theme) => ({
  hideOnMobile: {
    [theme.breakpoints.down('sm')]: {
      display: 'none',
    },
  },
  hideOnTablet: {
    [theme.breakpoints.down('md')]: {
      display: 'none',
    },
  },
  hideOnDesktop: {
    [theme.breakpoints.up('lg')]: {
      display: 'none',
    },
  },
  mobileOnly: {
    [theme.breakpoints.up('sm')]: {
      display: 'none',
    },
  },
  tabletUp: {
    [theme.breakpoints.down('sm')]: {
      display: 'none',
    },
  },
});

/**
 * Component-specific style utilities
 */
export const componentMixins = (theme: Theme) => ({
  card: {
    backgroundColor: theme.palette.background.paper,
    borderRadius: '12px',
    border: `1px solid ${theme.palette.divider}`,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.3s ease',
    '&:hover': {
      boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
      transform: 'translateY(-1px)',
    },
  },
  button: {
    borderRadius: '8px',
    textTransform: 'none' as const,
    fontWeight: theme.typography.fontWeightMedium,
    padding: '12px 24px',
    transition: 'all 0.3s ease',
    '&:hover': {
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
  },
  input: {
    '& .MuiOutlinedInput-root': {
      borderRadius: '8px',
      transition: 'all 0.3s ease',
      '&:hover': {
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.palette.primary.main,
        },
      },
      '&.Mui-focused': {
        '& .MuiOutlinedInput-notchedOutline': {
          borderWidth: '2px',
        },
      },
    },
  },
  dialog: {
    '& .MuiDialog-paper': {
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
    },
  },
  table: {
    '& .MuiTableCell-root': {
      borderBottom: `1px solid ${theme.palette.divider}`,
      padding: '16px',
    },
    '& .MuiTableHead-root': {
      backgroundColor: theme.palette.background.default,
    },
    '& .MuiTableRow-root:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
});

/**
 * Utility function to combine multiple mixins
 */
export const combineMixins = (...mixins: (CSSObject | undefined)[]): CSSObject => {
  return mixins.reduce((acc, mixin) => ({ ...acc, ...mixin }), {});
};
