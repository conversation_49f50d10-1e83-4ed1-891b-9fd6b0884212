import React, { useState } from 'react';
import { Content } from '@backstage/core-components';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import Fade from '@mui/material/Fade';
import { styled, useTheme } from '@mui/material/styles';
import CollectionIcon from '@mui/icons-material/CollectionsBookmark';
import K6Icon from '@mui/icons-material/Speed';
import TestingIcon from '@mui/icons-material/BugReport';
import { CollectionListPage } from '../CollectionListPage';
import { K6ConverterPage } from '../K6ConverterPage';
import { ApiTestingPage } from '../ApiTestingPage';
import { EnhancedHeader } from './EnhancedHeader';

// Styled components for enhanced design
const StyledTabsContainer = styled(Paper)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  overflow: 'hidden',
  border: `1px solid ${theme.palette.divider}`,
  background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  minHeight: 72,
  '& .MuiTabs-indicator': {
    height: 3,
    borderRadius: '3px 3px 0 0',
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  },
  '& .MuiTab-root': {
    minHeight: 72,
    textTransform: 'none',
    fontWeight: theme.typography.fontWeightMedium,
    fontSize: '16px',
    padding: theme.spacing(2, 4),
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative',
    overflow: 'hidden',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
      transform: 'translateY(-1px)',
      '&::before': {
        opacity: 0.1,
      },
    },
    '&.Mui-selected': {
      color: theme.palette.primary.main,
      fontWeight: theme.typography.fontWeightBold,
      '&::before': {
        opacity: 0.05,
      },
    },
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
      opacity: 0,
      transition: 'opacity 0.3s ease',
    },
  },
}));

const StyledTabPanel = styled(Box)(({ theme }) => ({
  padding: theme.spacing(4),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.spacing(2),
  boxShadow: '0 2px 12px rgba(0, 0, 0, 0.04)',
  border: `1px solid ${theme.palette.divider}`,
  marginTop: theme.spacing(3),
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  },
}));

const TabIconWrapper = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1.5),
  display: 'flex',
  alignItems: 'center',
  position: 'relative',
  zIndex: 1,
}));

const TabLabelWrapper = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  position: 'relative',
  zIndex: 1,
});

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`postman-converter-tabpanel-${index}`}
      aria-labelledby={`postman-converter-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Fade in={value === index} timeout={300}>
          <StyledTabPanel>
            {children}
          </StyledTabPanel>
        </Fade>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `postman-converter-tab-${index}`,
    'aria-controls': `postman-converter-tabpanel-${index}`,
  };
}

export const TabsPage = () => {
  const theme = useTheme();
  const [value, setValue] = useState(0);

  const handleChange = (_event: React.ChangeEvent<{}>, newValue: number) => {
    setValue(newValue);
  };

  const getTabIcon = (index: number, IconComponent: React.ComponentType<any>) => (
    <TabIconWrapper>
      <IconComponent
        style={{
          color: value === index ? theme.palette.primary.main : theme.palette.text.secondary,
          transition: 'color 0.3s ease',
        }}
      />
    </TabIconWrapper>
  );

  return (
    <Content>
      <EnhancedHeader />

      <StyledTabsContainer elevation={0}>
        <StyledTabs
          value={value}
          onChange={handleChange}
          aria-label="Postman converter tabs"
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab
            label={
              <TabLabelWrapper>
                {getTabIcon(0, CollectionIcon)}
                Collection Management
              </TabLabelWrapper>
            }
            {...a11yProps(0)}
          />
          <Tab
            label={
              <TabLabelWrapper>
                {getTabIcon(1, K6Icon)}
                K6 Converter
              </TabLabelWrapper>
            }
            {...a11yProps(1)}
          />
          <Tab
            label={
              <TabLabelWrapper>
                {getTabIcon(2, TestingIcon)}
                API Testing
              </TabLabelWrapper>
            }
            {...a11yProps(2)}
          />
        </StyledTabs>
        <Divider />
      </StyledTabsContainer>

      <Box>
        <TabPanel value={value} index={0}>
          <CollectionListPage />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <K6ConverterPage />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <ApiTestingPage />
        </TabPanel>
      </Box>
    </Content>
  );
};
