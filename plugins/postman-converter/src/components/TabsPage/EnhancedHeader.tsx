import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import { styled, useTheme } from '@mui/material/styles';
import { ContentHeader, SupportButton } from '@backstage/core-components';
import ApiIcon from '@mui/icons-material/Api';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import SecurityIcon from '@mui/icons-material/Security';

const StyledHeaderContainer = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}10 100%)`,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  border: `1px solid ${theme.palette.divider}`,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 20% 80%, ${theme.palette.primary.main}20 0%, transparent 50%),
                 radial-gradient(circle at 80% 20%, ${theme.palette.secondary.main}15 0%, transparent 50%)`,
    pointerEvents: 'none',
  },
}));

const FeatureChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  '& .MuiChip-icon': {
    color: theme.palette.primary.main,
  },
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    transform: 'translateY(-1px)',
    boxShadow: theme.shadows[2],
  },
  transition: 'all 0.2s ease',
}));

const HeaderContent = styled(Box)({
  position: 'relative',
  zIndex: 1,
});

export const EnhancedHeader: React.FC = () => {
  const theme = useTheme();

  return (
    <StyledHeaderContainer>
      <HeaderContent>
        <ContentHeader title="Postman Converter">
          <SupportButton>
            Manage Postman collections, convert to K6 scripts, and run API tests
          </SupportButton>
        </ContentHeader>
        
        <Typography 
          variant="body1" 
          color="textSecondary" 
          sx={{ mb: 2, maxWidth: '600px' }}
        >
          A comprehensive toolkit for managing Postman collections, converting them to K6 performance tests, 
          and running API tests directly in your development workflow.
        </Typography>

        <Box display="flex" flexWrap="wrap" gap={1}>
          <FeatureChip
            icon={<ApiIcon />}
            label="Collection Management"
            variant="outlined"
            size="small"
          />
          <FeatureChip
            icon={<TrendingUpIcon />}
            label="K6 Performance Testing"
            variant="outlined"
            size="small"
          />
          <FeatureChip
            icon={<SecurityIcon />}
            label="API Testing"
            variant="outlined"
            size="small"
          />
        </Box>
      </HeaderContent>
    </StyledHeaderContainer>
  );
};
