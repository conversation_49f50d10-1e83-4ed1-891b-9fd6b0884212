import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { styled, useTheme } from '@mui/material/styles';
import { SvgIcon } from '@mui/material';

const EmptyStateContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(6),
  minHeight: '400px',
  textAlign: 'center',
  background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
  borderRadius: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 50% 50%, ${theme.palette.primary.main}05 0%, transparent 70%)`,
    pointerEvents: 'none',
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  padding: theme.spacing(3),
  borderRadius: '50%',
  backgroundColor: theme.palette.action.hover,
  border: `2px solid ${theme.palette.divider}`,
  position: 'relative',
  zIndex: 1,
  '& .MuiSvgIcon-root': {
    fontSize: '48px',
    color: theme.palette.text.secondary,
  },
}));

const Title = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  fontWeight: theme.typography.fontWeightBold,
  color: theme.palette.text.primary,
  position: 'relative',
  zIndex: 1,
}));

const Description = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  color: theme.palette.text.secondary,
  maxWidth: '400px',
  lineHeight: 1.6,
  position: 'relative',
  zIndex: 1,
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(3),
  padding: theme.spacing(1.5, 4),
  textTransform: 'none',
  fontWeight: theme.typography.fontWeightMedium,
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  position: 'relative',
  zIndex: 1,
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.15)',
  },
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
}));

interface EnhancedEmptyStateProps {
  icon?: React.ComponentType<any>;
  title: string;
  description: string;
  actionLabel?: string;
  onAction?: () => void;
  actionIcon?: React.ComponentType<any>;
}

export const EnhancedEmptyState: React.FC<EnhancedEmptyStateProps> = ({
  icon: IconComponent,
  title,
  description,
  actionLabel,
  onAction,
  actionIcon: ActionIcon,
}) => {
  const theme = useTheme();

  return (
    <EmptyStateContainer>
      {IconComponent && (
        <IconWrapper>
          <IconComponent />
        </IconWrapper>
      )}
      
      <Title variant="h5">
        {title}
      </Title>
      
      <Description variant="body1">
        {description}
      </Description>
      
      {actionLabel && onAction && (
        <ActionButton
          variant="contained"
          color="primary"
          onClick={onAction}
          startIcon={ActionIcon ? <ActionIcon /> : undefined}
        >
          {actionLabel}
        </ActionButton>
      )}
    </EmptyStateContainer>
  );
};
