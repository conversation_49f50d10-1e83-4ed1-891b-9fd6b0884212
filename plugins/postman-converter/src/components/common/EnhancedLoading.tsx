import React from 'react';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import { styled, keyframes } from '@mui/material/styles';

const pulse = keyframes`
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
`;

const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(6),
  minHeight: '300px',
  background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
  borderRadius: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
}));

const StyledProgress = styled(CircularProgress)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  '& .MuiCircularProgress-circle': {
    strokeLinecap: 'round',
  },
}));

const LoadingText = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  animation: `${pulse} 2s ease-in-out infinite`,
  textAlign: 'center',
  maxWidth: '300px',
}));

const LoadingDots = styled(Box)({
  display: 'flex',
  gap: '4px',
  marginTop: '8px',
});

const Dot = styled(Box)(({ theme }) => ({
  width: '6px',
  height: '6px',
  borderRadius: '50%',
  backgroundColor: theme.palette.primary.main,
  animation: `${pulse} 1.4s ease-in-out infinite both`,
  '&:nth-of-type(1)': {
    animationDelay: '-0.32s',
  },
  '&:nth-of-type(2)': {
    animationDelay: '-0.16s',
  },
  '&:nth-of-type(3)': {
    animationDelay: '0s',
  },
}));

interface EnhancedLoadingProps {
  message?: string;
  size?: number;
  showDots?: boolean;
}

export const EnhancedLoading: React.FC<EnhancedLoadingProps> = ({
  message = 'Loading...',
  size = 48,
  showDots = true,
}) => {
  return (
    <LoadingContainer>
      <StyledProgress size={size} thickness={4} />
      <LoadingText variant="body1">
        {message}
      </LoadingText>
      {showDots && (
        <LoadingDots>
          <Dot />
          <Dot />
          <Dot />
        </LoadingDots>
      )}
    </LoadingContainer>
  );
};
