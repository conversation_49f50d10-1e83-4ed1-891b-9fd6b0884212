import React from 'react';
import { Collection, CollectionVersion } from '../../types';

/**
 * Props for the TabPanel component
 */
export interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

/**
 * Collection detail page state interface
 */
export interface CollectionDetailPageState {
  tabValue: number;
  isDeleteDialogOpen: boolean;
}

/**
 * Collection detail page handlers interface
 */
export interface CollectionDetailPageHandlers {
  handleTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  handleEditClick: () => void;
  handleDeleteClick: () => void;
  handleDeleteConfirm: () => Promise<void>;
  handleRollback: (versionNumber: number) => Promise<void>;
}

/**
 * Collection header component props
 */
export interface CollectionHeaderProps {
  collection: Collection;
  onEdit: () => void;
  onDelete: () => void;
}

/**
 * Collection metadata display props
 */
export interface CollectionMetadataProps {
  collection: Collection;
}

/**
 * Collection tabs component props
 */
export interface CollectionTabsProps {
  collection: Collection;
  versions: CollectionVersion[];
  versionsLoading: boolean;
  tabValue: number;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onRollback: (versionNumber: number) => Promise<void>;
}

/**
 * Version history table props
 */
export interface VersionHistoryProps {
  versions: CollectionVersion[];
  currentVersion?: number;
  loading?: boolean;
  onRollback: (versionNumber: number) => Promise<void>;
}

/**
 * Collection content display props
 */
export interface CollectionContentProps {
  content?: string;
}

/**
 * Delete confirmation dialog props
 */
export interface DeleteConfirmationDialogProps {
  open: boolean;
  collectionName: string;
  onClose: () => void;
  onConfirm: () => Promise<void>;
}

/**
 * Hook return type for collection data
 */
export interface UseCollectionDataReturn {
  collection: Collection | undefined;
  loading: boolean;
  error: Error | undefined;
  versions: CollectionVersion[];
  versionsLoading: boolean;
}

/**
 * Hook return type for collection actions
 */
export interface UseCollectionActionsReturn {
  tabValue: number;
  isDeleteDialogOpen: boolean;
  handleTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  handleEditClick: () => void;
  handleDeleteClick: () => void;
  handleDeleteConfirm: () => Promise<void>;
  handleRollback: (versionNumber: number) => Promise<void>;
  setIsDeleteDialogOpen: (open: boolean) => void;
}
