/**
 * Utility functions for collection content parsing and formatting
 */

/**
 * Parsed collection content interface
 */
export interface ParsedCollectionContent {
  isValid: boolean;
  name?: string;
  description?: string;
  hasInfo: boolean;
  error?: string;
}

/**
 * Parse collection content JSON and extract metadata
 */
export function parseCollectionContent(content?: string): ParsedCollectionContent {
  if (!content) {
    return {
      isValid: false,
      hasInfo: false,
      error: 'No content provided',
    };
  }

  try {
    const parsed = JSON.parse(content);
    
    // Check for Postman collection v2.1 format
    if (parsed.info && parsed.info.name) {
      return {
        isValid: true,
        hasInfo: true,
        name: parsed.info.name,
        description: parsed.info.description,
      };
    }
    
    // Check for legacy format
    if (parsed.name) {
      return {
        isValid: true,
        hasInfo: false,
        name: parsed.name,
        description: parsed.description,
      };
    }
    
    // Valid JSON but no recognizable collection structure
    return {
      isValid: true,
      hasInfo: false,
      name: 'Postman Collection',
    };
  } catch (error) {
    return {
      isValid: false,
      hasInfo: false,
      error: 'Invalid JSON format',
    };
  }
}

/**
 * Format date for display
 */
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString();
}

/**
 * Format date and time for display
 */
export function formatDateTime(dateString: string): {
  date: string;
  time: string;
} {
  const date = new Date(dateString);
  return {
    date: date.toLocaleDateString(),
    time: date.toLocaleTimeString(),
  };
}

/**
 * Get user initials from user ID
 */
export function getUserInitials(userId: string): string {
  return userId.charAt(0).toUpperCase();
}

/**
 * Check if a version is the current version
 */
export function isCurrentVersion(version: number, currentVersion?: number): boolean {
  return version === currentVersion;
}
