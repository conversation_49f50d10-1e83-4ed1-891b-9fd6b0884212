import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Alert from '@mui/material/Alert';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import DeleteIcon from '@mui/icons-material/Delete';
import { DeleteConfirmationDialogProps } from '../CollectionDetailPage.types';
import { ActionButton } from '../CollectionDetailPage.styles';

/**
 * Delete confirmation dialog component
 */
export function DeleteConfirmationDialog({
  open,
  collectionName,
  onClose,
  onConfirm,
}: DeleteConfirmationDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center">
          <DeleteIcon sx={{ mr: 1, color: 'error.main' }} />
          <Typography variant="h6" component="span">
            Delete Collection
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Alert severity="warning" sx={{ mb: 2 }}>
          This action cannot be undone!
        </Alert>
        <Typography variant="body1">
          Are you sure you want to delete the collection <strong>"{collectionName}"</strong>?
          All associated data including version history will be permanently removed.
        </Typography>
      </DialogContent>
      <DialogActions sx={{ p: 3, pt: 2 }}>
        <ActionButton
          onClick={onClose}
          variant="outlined"
          color="inherit"
        >
          Cancel
        </ActionButton>
        <ActionButton
          onClick={onConfirm}
          variant="contained"
          color="error"
          startIcon={<DeleteIcon />}
        >
          Delete Collection
        </ActionButton>
      </DialogActions>
    </Dialog>
  );
}
