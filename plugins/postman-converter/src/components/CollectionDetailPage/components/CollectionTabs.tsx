import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import CollectionIcon from '@mui/icons-material/CollectionsBookmark';
import HistoryIcon from '@mui/icons-material/History';
import { CollectionTabsProps } from '../CollectionDetailPage.types';
import { StyledTabs, TabContentCard } from '../CollectionDetailPage.styles';
import { TabPanel } from './TabPanel';
import { CollectionContent } from './CollectionContent';
import { VersionHistory } from './VersionHistory';

/**
 * Collection tabs component containing content and version history tabs
 */
export function CollectionTabs({
  collection,
  versions,
  versionsLoading,
  tabValue,
  onTabChange,
  onRollback,
}: CollectionTabsProps) {
  return (
    <Box>
      <StyledTabs
        value={tabValue}
        onChange={onTabChange}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab
          label="Collection Content"
          icon={<CollectionIcon />}
          iconPosition="start"
          sx={{ fontSize: '16px', fontWeight: 'medium' }}
        />
        <Tab
          label="Version History"
          icon={<HistoryIcon />}
          iconPosition="start"
          sx={{ fontSize: '16px', fontWeight: 'medium' }}
        />
      </StyledTabs>

      <TabContentCard>
        <TabPanel value={tabValue} index={0}>
          <CollectionContent content={collection.content} />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <VersionHistory
            versions={versions}
            currentVersion={collection.version}
            loading={versionsLoading}
            onRollback={onRollback}
          />
        </TabPanel>
      </TabContentCard>
    </Box>
  );
}
