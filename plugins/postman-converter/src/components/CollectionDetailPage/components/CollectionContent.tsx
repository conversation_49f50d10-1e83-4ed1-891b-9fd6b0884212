import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import { useTheme } from '@mui/material/styles';
import { CodeSnippet } from '@backstage/core-components';
import CollectionIcon from '@mui/icons-material/CollectionsBookmark';
import { CollectionContentProps } from '../CollectionDetailPage.types';
import { SectionTitle, ContentSection, EnhancedAlert } from '../CollectionDetailPage.styles';
import { getEnhancedBorderRadius, getEnhancedGradients, getEnhancedShadows } from '../../../theme';
import { parseCollectionContent } from '../utils/collectionUtils';

/**
 * Collection content component displaying parsed collection data and JSON content
 */
export function CollectionContent({ content }: CollectionContentProps) {
  const theme = useTheme();
  const parsedContent = parseCollectionContent(content);

  if (!content) {
    return (
      <EnhancedAlert severity="info">
        No content available for this collection
      </EnhancedAlert>
    );
  }

  const renderCollectionInfo = () => {
    if (!parsedContent.isValid) {
      return (
        <Chip
          label={parsedContent.error || "Invalid Collection Format"}
          variant="outlined"
          color="error"
          size="medium"
          sx={{
            fontSize: '16px',
            height: '40px',
            borderRadius: getEnhancedBorderRadius(theme).medium,
          }}
        />
      );
    }

    if (parsedContent.hasInfo && parsedContent.name) {
      return (
        <Box>
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6}>
              <Chip
                label={`Collection: ${parsedContent.name}`}
                variant="outlined"
                color="primary"
                size="medium"
                sx={{
                  fontSize: '16px',
                  height: '40px',
                  borderRadius: getEnhancedBorderRadius(theme).medium,
                }}
              />
            </Grid>
          </Grid>
          {parsedContent.description && (
            <Box
              sx={{
                p: 3,
                backgroundColor: getEnhancedGradients(theme).primarySubtle,
                borderRadius: getEnhancedBorderRadius(theme).medium,
                border: `1px solid ${theme.palette.primary.main}25`,
                mb: 4,
              }}
            >
              <Typography variant="body1" color="text.primary">
                <strong>Description:</strong> {parsedContent.description}
              </Typography>
            </Box>
          )}
        </Box>
      );
    }

    if (parsedContent.name) {
      return (
        <Chip
          label={`Collection: ${parsedContent.name}`}
          variant="outlined"
          color="primary"
          size="medium"
          sx={{
            fontSize: '16px',
            height: '40px',
            borderRadius: getEnhancedBorderRadius(theme).medium,
          }}
        />
      );
    }

    return (
      <Chip
        label="Postman Collection"
        variant="outlined"
        color="default"
        size="medium"
        sx={{
          fontSize: '16px',
          height: '40px',
          borderRadius: getEnhancedBorderRadius(theme).medium,
        }}
      />
    );
  };

  return (
    <>
      <SectionTitle>
        <CollectionIcon sx={{ color: 'primary.main' }} />
        Collection Content
      </SectionTitle>
      <ContentSection>
        {renderCollectionInfo()}
      </ContentSection>
      <Divider sx={{ mb: 4 }} />
      <ContentSection>
        <Typography
          variant="h6"
          gutterBottom
          sx={{
            mb: 3,
            fontWeight: 'bold',
            fontSize: '20px',
          }}
        >
          JSON Content
        </Typography>
        <Box
          sx={{
            borderRadius: getEnhancedBorderRadius(theme).medium,
            overflow: 'hidden',
            border: `1px solid ${theme.palette.divider}`,
            boxShadow: getEnhancedShadows(theme).panel,
          }}
        >
          <CodeSnippet
            text={content}
            language="json"
            showCopyCodeButton
          />
        </Box>
      </ContentSection>
    </>
  );
}
