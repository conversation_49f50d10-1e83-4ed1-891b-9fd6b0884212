import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import Avatar from '@mui/material/Avatar';
import PersonIcon from '@mui/icons-material/Person';
import CalendarIcon from '@mui/icons-material/CalendarToday';
import VersionIcon from '@mui/icons-material/Label';
import { CollectionMetadataProps } from '../CollectionDetailPage.types';
import {
  MetadataCard,
  MetadataItem,
  MetadataContent,
  MetadataLabel,
  MetadataValue,
} from '../CollectionDetailPage.styles';
import { formatDate, getUserInitials } from '../utils/collectionUtils';

/**
 * Collection metadata component displaying creation date, owner, version, etc.
 */
export function CollectionMetadata({ collection }: CollectionMetadataProps) {
  return (
    <MetadataCard>
      <CardContent sx={{ p: 3 }}>
        <Typography
          variant="h5"
          gutterBottom
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 3,
            fontWeight: 'bold',
            fontSize: '20px',
          }}
        >
          <VersionIcon sx={{ mr: 1.5, color: 'primary.main', fontSize: '24px' }} />
          Collection Information
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} lg={3}>
            <MetadataItem>
              <CalendarIcon sx={{ color: 'primary.main', fontSize: '20px', flexShrink: 0 }} />
              <MetadataContent>
                <MetadataLabel>Created</MetadataLabel>
                <MetadataValue>
                  <Chip
                    label={formatDate(collection.created_at)}
                    size="small"
                    variant="outlined"
                    color="primary"
                    sx={{ fontSize: '12px', height: '24px' }}
                  />
                </MetadataValue>
              </MetadataContent>
            </MetadataItem>
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <MetadataItem>
              <CalendarIcon sx={{ color: 'secondary.main', fontSize: '20px', flexShrink: 0 }} />
              <MetadataContent>
                <MetadataLabel>Last Updated</MetadataLabel>
                <MetadataValue>
                  <Chip
                    label={formatDate(collection.updated_at)}
                    size="small"
                    variant="filled"
                    color="secondary"
                    sx={{ fontSize: '12px', height: '24px' }}
                  />
                </MetadataValue>
              </MetadataContent>
            </MetadataItem>
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <MetadataItem>
              <PersonIcon sx={{ color: 'info.main', fontSize: '20px', flexShrink: 0 }} />
              <MetadataContent>
                <MetadataLabel>Owner</MetadataLabel>
                <MetadataValue>
                  <Avatar sx={{ width: 20, height: 20, fontSize: '10px', mr: 0.5 }}>
                    {getUserInitials(collection.owner_id)}
                  </Avatar>
                  <Typography
                    variant="body2"
                    fontWeight="medium"
                    sx={{
                      fontSize: '14px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      minWidth: 0,
                      flex: 1,
                    }}
                    title={collection.owner_id}
                  >
                    {collection.owner_id}
                  </Typography>
                </MetadataValue>
              </MetadataContent>
            </MetadataItem>
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <MetadataItem>
              <VersionIcon sx={{ color: 'success.main', fontSize: '20px', flexShrink: 0 }} />
              <MetadataContent>
                <MetadataLabel>Current Version</MetadataLabel>
                <MetadataValue>
                  <Chip
                    label={`v${collection.version || '1.0'}`}
                    size="small"
                    variant="filled"
                    color="success"
                    sx={{ fontSize: '12px', height: '24px', fontWeight: 'bold' }}
                  />
                </MetadataValue>
              </MetadataContent>
            </MetadataItem>
          </Grid>
        </Grid>
      </CardContent>
    </MetadataCard>
  );
}
