import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CollectionIcon from '@mui/icons-material/CollectionsBookmark';
import { CollectionHeaderProps } from '../CollectionDetailPage.types';
import {
  HeaderCard,
  HeaderContent,
  HeaderMain,
  HeaderInfo,
  HeaderActions,
  ActionButton,
} from '../CollectionDetailPage.styles';

/**
 * Collection header component displaying title, description, and action buttons
 */
export function CollectionHeader({ collection, onEdit, onDelete }: CollectionHeaderProps) {
  return (
    <HeaderCard>
      <HeaderContent>
        <HeaderMain>
          <HeaderInfo>
            <Box display="flex" alignItems="center" mb={2}>
              <CollectionIcon sx={{ mr: 2, color: 'primary.main', fontSize: '40px' }} />
              <Typography
                variant="h3"
                component="h1"
                fontWeight="bold"
                sx={{
                  wordBreak: 'break-word',
                  lineHeight: 1.2,
                }}
              >
                {collection.name}
              </Typography>
            </Box>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                fontSize: '17.6px',
                lineHeight: 1.6,
                maxWidth: '100%',
              }}
            >
              {collection.description || 'No description provided for this collection.'}
            </Typography>
          </HeaderInfo>
          <HeaderActions>
            <ActionButton
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              onClick={onEdit}
              size="large"
            >
              Edit Collection
            </ActionButton>
            <ActionButton
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={onDelete}
              size="large"
            >
              Delete
            </ActionButton>
          </HeaderActions>
        </HeaderMain>
      </HeaderContent>
    </HeaderCard>
  );
}
