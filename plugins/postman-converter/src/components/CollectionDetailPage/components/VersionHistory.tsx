import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Avatar from '@mui/material/Avatar';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { useTheme } from '@mui/material/styles';
import HistoryIcon from '@mui/icons-material/History';
import VersionIcon from '@mui/icons-material/Label';
import RestoreIcon from '@mui/icons-material/Restore';
import { VersionHistoryProps } from '../CollectionDetailPage.types';
import { SectionTitle, ActionButton, StyledVersionTable, EnhancedAlert } from '../CollectionDetailPage.styles';
import { EnhancedLoading } from '../../common';
import { formatDateTime, getUserInitials, isCurrentVersion } from '../utils/collectionUtils';
import { getEnhancedBorderRadius } from '../../../theme';

/**
 * Version history component displaying collection version table
 */
export function VersionHistory({ versions, currentVersion, loading, onRollback }: VersionHistoryProps) {
  const theme = useTheme();

  if (loading) {
    return <EnhancedLoading message="Loading version history..." />;
  }

  if (!versions || versions.length === 0) {
    return (
      <>
        <SectionTitle>
          <HistoryIcon sx={{ color: 'primary.main' }} />
          Version History
        </SectionTitle>
        <EnhancedAlert severity="info">
          No version history available for this collection
        </EnhancedAlert>
      </>
    );
  }

  return (
    <>
      <SectionTitle>
        <HistoryIcon sx={{ color: 'primary.main' }} />
        Version History
      </SectionTitle>
      <StyledVersionTable>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '16px', py: 3 }}>Version</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '16px', py: 3 }}>Created</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '16px', py: 3 }}>User</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold', fontSize: '16px', py: 3 }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {versions.map((version) => {
              const isCurrent = isCurrentVersion(version.version, currentVersion);
              const { date, time } = formatDateTime(version.created_at);
              
              return (
                <TableRow key={version.id}>
                  <TableCell sx={{ py: 3 }}>
                    <Chip
                      label={`v${version.version}`}
                      size="medium"
                      variant={isCurrent ? "filled" : "outlined"}
                      color={isCurrent ? "primary" : "default"}
                      sx={{
                        fontSize: '14px',
                        fontWeight: 'bold',
                        borderRadius: getEnhancedBorderRadius(theme).medium,
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ py: 3 }}>
                    <Box>
                      <Typography variant="body1" fontWeight="medium">
                        {date}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {time}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ py: 3 }}>
                    <Box display="flex" alignItems="center">
                      <Avatar
                        sx={{
                          width: 36,
                          height: 36,
                          mr: 2,
                          fontSize: '14px',
                          bgcolor: 'primary.main',
                        }}
                      >
                        {getUserInitials(version.user_id)}
                      </Avatar>
                      <Typography variant="body1" fontWeight="medium">
                        {version.user_id}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="right" sx={{ py: 3 }}>
                    <ActionButton
                      size="medium"
                      variant={isCurrent ? "contained" : "outlined"}
                      color={isCurrent ? "success" : "primary"}
                      startIcon={isCurrent ? <VersionIcon /> : <RestoreIcon />}
                      onClick={() => onRollback(version.version)}
                      disabled={isCurrent}
                    >
                      {isCurrent ? 'Current' : 'Restore'}
                    </ActionButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </StyledVersionTable>
    </>
  );
}
