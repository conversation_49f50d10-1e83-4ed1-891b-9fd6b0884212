import { TabPanelProps } from '../CollectionDetailPage.types';
import { TabPanelContent } from '../CollectionDetailPage.styles';

/**
 * Generic tab panel component for displaying tab content
 */
export function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <TabPanelContent>{children}</TabPanelContent>}
    </div>
  );
}
