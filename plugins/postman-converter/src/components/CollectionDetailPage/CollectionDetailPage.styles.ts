import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Tabs from '@mui/material/Tabs';
import Button from '@mui/material/Button';
import TableContainer from '@mui/material/TableContainer';
import Alert from '@mui/material/Alert';

import {
  getContainerStyles,
  getElevatedCardStyles,
  getButtonStyles,
  getEnhancedSpacing,
  getEnhancedBorderRadius,
  getEnhancedShadows,
  getEnhancedGradients,
} from '../../theme';

// Container and Layout Styles
export const StyledContainer = styled(Box)(({ theme }) => ({
  ...getContainerStyles(theme),
}));

export const HeaderCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  marginBottom: getEnhancedSpacing(theme).xl,
}));

export const HeaderContent = styled(CardContent)(({ theme }) => ({
  padding: getEnhancedSpacing(theme).xl,
  [theme.breakpoints.down('md')]: {
    padding: getEnhancedSpacing(theme).lg,
  },
  [theme.breakpoints.down('sm')]: {
    padding: getEnhancedSpacing(theme).md,
  },
}));

export const HeaderMain = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  gap: getEnhancedSpacing(theme).lg,
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: getEnhancedSpacing(theme).md,
  },
}));

export const HeaderInfo = styled(Box)(() => ({
  flex: 1,
  minWidth: 0, // Allows text to truncate properly
}));

export const HeaderActions = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: getEnhancedSpacing(theme).md,
  flexShrink: 0,
  [theme.breakpoints.down('md')]: {
    justifyContent: 'flex-end',
  },
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    gap: getEnhancedSpacing(theme).sm,
  },
}));

// Metadata Section Styles
export const MetadataCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  marginBottom: getEnhancedSpacing(theme).xl,
}));

export const MetadataItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: getEnhancedSpacing(theme).md,
  backgroundColor: getEnhancedGradients(theme).backgroundElevated,
  borderRadius: getEnhancedBorderRadius(theme).medium,
  border: `1px solid ${theme.palette.divider}`,
  height: '100%',
  minHeight: '80px',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: getEnhancedShadows(theme).cardHover,
    borderColor: theme.palette.primary.main,
  },
}));

export const MetadataContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: getEnhancedSpacing(theme).xs,
  flex: 1,
  minWidth: 0,
  marginLeft: getEnhancedSpacing(theme).sm,
  overflow: 'hidden', // Prevent content overflow
}));

export const MetadataLabel = styled(Typography)(({ theme }) => ({
  fontSize: '12px',
  fontWeight: theme.typography.fontWeightBold,
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
  color: theme.palette.text.secondary,
  lineHeight: 1.2,
  marginBottom: getEnhancedSpacing(theme).xs,
}));

export const MetadataValue = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: getEnhancedSpacing(theme).xs,
  minWidth: 0, // Allow content to shrink
  overflow: 'hidden', // Prevent overflow
}));

// Tab Section Styles
export const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderRadius: getEnhancedBorderRadius(theme).large,
  borderBottomLeftRadius: 0,
  borderBottomRightRadius: 0,
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: getEnhancedShadows(theme).panel,
  '& .MuiTabs-indicator': {
    height: 4,
    borderRadius: '4px 4px 0 0',
    background: getEnhancedGradients(theme).primary,
  },
  '& .MuiTab-root': {
    textTransform: 'none',
    fontWeight: theme.typography.fontWeightMedium,
    fontSize: '16px',
    padding: getEnhancedSpacing(theme).lg,
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
      transform: 'translateY(-1px)',
    },
  },
}));

export const TabContentCard = styled(Card)(({ theme }) => ({
  borderRadius: 0,
  borderBottomLeftRadius: getEnhancedBorderRadius(theme).large,
  borderBottomRightRadius: getEnhancedBorderRadius(theme).large,
  boxShadow: getEnhancedShadows(theme).card,
  border: `1px solid ${theme.palette.divider}`,
  borderTop: 'none',
}));

export const TabPanelContent = styled(Box)(({ theme }) => ({
  padding: getEnhancedSpacing(theme).xl,
  [theme.breakpoints.down('md')]: {
    padding: getEnhancedSpacing(theme).lg,
  },
  [theme.breakpoints.down('sm')]: {
    padding: getEnhancedSpacing(theme).md,
  },
}));

// Content Section Styles
export const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '24px',
  fontWeight: theme.typography.fontWeightBold,
  marginBottom: getEnhancedSpacing(theme).xl,
  display: 'flex',
  alignItems: 'center',
  '& .MuiSvgIcon-root': {
    marginRight: getEnhancedSpacing(theme).md,
    fontSize: '28px',
  },
}));

export const ContentSection = styled(Box)(({ theme }) => ({
  marginBottom: getEnhancedSpacing(theme).xl,
}));

// Button and Interactive Elements
export const ActionButton = styled(Button)(({ theme }) => ({
  ...getButtonStyles(theme),
}));

// Table Styles
export const StyledVersionTable = styled(TableContainer)(({ theme }) => ({
  borderRadius: getEnhancedBorderRadius(theme).medium,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: getEnhancedShadows(theme).panel,
  '& .MuiTableHead-root': {
    backgroundColor: getEnhancedGradients(theme).primarySubtle,
  },
  '& .MuiTableCell-head': {
    fontWeight: theme.typography.fontWeightBold,
    color: theme.palette.text.primary,
    fontSize: '16px',
  },
  '& .MuiTableRow-root:hover': {
    backgroundColor: theme.palette.action.hover,
    transform: 'scale(1.01)',
  },
  '& .MuiTableRow-root': {
    transition: 'all 0.2s ease',
  },
}));

// Alert Styles
export const EnhancedAlert = styled(Alert)(({ theme }) => ({
  borderRadius: getEnhancedBorderRadius(theme).medium,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: getEnhancedShadows(theme).panel,
}));
