import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { postmanConverterApiRef } from '../../../api';
import { UseCollectionActionsReturn } from '../CollectionDetailPage.types';

/**
 * Custom hook for collection action handlers
 */
export function useCollectionActions(id?: string): UseCollectionActionsReturn {
  const navigate = useNavigate();
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);
  
  const [tabValue, setTabValue] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleTabChange = (_: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEditClick = () => {
    if (id) {
      navigate(`/postman-converter/${id}/edit`);
    }
  };

  const handleDeleteClick = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!id) return;
    try {
      await postmanConverterApi.deleteCollection(id);
      setIsDeleteDialogOpen(false);
      navigate('/postman-converter');
    } catch (e) {
      errorApi.post(e as any);
    }
  };

  const handleRollback = async (versionNumber: number) => {
    if (!id) return;
    try {
      await postmanConverterApi.rollbackToVersion(id, versionNumber);
      // Refresh the page to show updated data
      window.location.reload();
    } catch (e) {
      errorApi.post(e as any);
    }
  };

  return {
    tabValue,
    isDeleteDialogOpen,
    handleTabChange,
    handleEditClick,
    handleDeleteClick,
    handleDeleteConfirm,
    handleRollback,
    setIsDeleteDialogOpen,
  };
}
