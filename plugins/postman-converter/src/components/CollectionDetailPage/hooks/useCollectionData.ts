import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { useAsync } from 'react-use';
import { postmanConverterApiRef } from '../../../api';
import { UseCollectionDataReturn } from '../CollectionDetailPage.types';

/**
 * Custom hook for fetching collection data and versions
 */
export function useCollectionData(id?: string): UseCollectionDataReturn {
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  const { value: collection, loading, error } = useAsync(async () => {
    if (!id) return undefined;
    try {
      return await postmanConverterApi.getCollectionById(id);
    } catch (e) {
      errorApi.post(e as any);
      throw e;
    }
  }, [id]);

  const { value: versions = [], loading: versionsLoading } = useAsync(async () => {
    if (!id) return [];
    try {
      return await postmanConverterApi.getCollectionVersions(id);
    } catch (e) {
      errorApi.post(e as any);
      return [];
    }
  }, [id]);

  return {
    collection,
    loading,
    error,
    versions,
    versionsLoading,
  };
}
