import { useParams } from 'react-router-dom';
import { Content, ErrorPanel } from '@backstage/core-components';
import Alert from '@mui/material/Alert';
import { EnhancedLoading } from '../common';

// Import components and hooks
import {
  CollectionHeader,
  CollectionMetadata,
  CollectionTabs,
  DeleteConfirmationDialog,
} from './components';
import { useCollectionData, useCollectionActions } from './hooks';
import { StyledContainer } from './CollectionDetailPage.styles';

/**
 * Collection detail page component displaying collection information, content, and version history
 */
export const CollectionDetailPage = () => {
  const { id } = useParams<{ id: string }>();

  // Use custom hooks for data fetching and actions
  const { collection, loading, error, versions, versionsLoading } = useCollectionData(id);
  const {
    tabValue,
    isDeleteDialogOpen,
    handleTabChange,
    handleEditClick,
    handleDeleteClick,
    handleDeleteConfirm,
    handleRollback,
    setIsDeleteDialogOpen,
  } = useCollectionActions(id);

  // Loading and error states
  if (loading) {
    return <EnhancedLoading message="Loading collection details..." />;
  }

  if (error) {
    return <ErrorPanel error={error} />;
  }

  if (!collection) {
    return <Alert severity="error">Collection not found</Alert>;
  }

  return (
    <Content>
      <StyledContainer>
        {/* Header Section */}
        <CollectionHeader
          collection={collection}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
        />

        {/* Metadata Section */}
        <CollectionMetadata collection={collection} />

        {/* Tabs Section */}
        <CollectionTabs
          collection={collection}
          versions={versions}
          versionsLoading={versionsLoading}
          tabValue={tabValue}
          onTabChange={handleTabChange}
          onRollback={handleRollback}
        />
      </StyledContainer>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        collectionName={collection.name}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteConfirm}
      />
    </Content>
  );
};