import React from 'react';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import CodeIcon from '@mui/icons-material/Code';

interface GenerateScriptButtonProps {
  isGenerating: boolean;
  onClick: () => void;
}

/**
 * Button component for generating K6 scripts
 */
export const GenerateScriptButton: React.FC<GenerateScriptButtonProps> = ({
  isGenerating,
  onClick,
}) => {
  return (
    <Button
      variant="contained"
      color="primary"
      fullWidth
      onClick={onClick}
      disabled={isGenerating}
      startIcon={isGenerating ? <CircularProgress size={20} /> : <CodeIcon />}
    >
      {isGenerating ? 'Generating...' : 'Generate K6 Script'}
    </Button>
  );
};
