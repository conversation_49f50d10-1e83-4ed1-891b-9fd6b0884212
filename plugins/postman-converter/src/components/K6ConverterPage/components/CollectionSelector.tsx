import React from 'react';
import Typography from '@mui/material/Typography';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import makeStyles from '@mui/styles/makeStyles';
import { Collection } from '../../../types';

const useStyles = makeStyles(theme => ({
  formControl: {
    width: '100%',
    marginBottom: theme.spacing(2),
  },
}));

interface CollectionSelectorProps {
  collections: Collection[];
  selectedCollectionId: string;
  onCollectionChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
}

/**
 * Component for selecting a collection
 */
export const CollectionSelector: React.FC<CollectionSelectorProps> = ({
  collections,
  selectedCollectionId,
  onCollectionChange,
}) => {
  const classes = useStyles();

  return (
    <>
      <Typography variant="h6" gutterBottom>
        1. Select Collection
      </Typography>
      <FormControl className={classes.formControl}>
        <InputLabel id="collection-select-label">Collection</InputLabel>
        <Select
          labelId="collection-select-label"
          id="collection-select"
          value={selectedCollectionId}
          onChange={onCollectionChange}
        >
          <MenuItem value="">
            <em>Select a collection</em>
          </MenuItem>
          {collections.map(collection => (
            <MenuItem key={collection.id} value={collection.id}>
              {collection.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </>
  );
};
