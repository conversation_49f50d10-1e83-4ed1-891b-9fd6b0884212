import React, { useState } from 'react';
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Snackbar from '@mui/material/Snackbar';
import Button from '@mui/material/Button';
import Alert from '@mui/material/Alert';
import { styled } from '@mui/material/styles';
import {
  ErrorPanel,
} from '@backstage/core-components';
import AddIcon from '@mui/icons-material/Add';
import K6Icon from '@mui/icons-material/Speed';

// Import custom hooks
import { useCollections, useK6Converter } from './hooks';

// Import components
import {
  K6ConverterHeader,
  CollectionSelector,
  FolderSelector,
  ConfigurationPanel,
  ScriptOutputPanel,
  GenerateScriptButton,
} from './components';

// Import utilities
import { copyToClipboard } from './utils';
import { EnhancedLoading, EnhancedEmptyState } from '../common';

// Styled components
const StyledContainer = styled(Box)(({ theme }) => ({
  height: '100%',
  padding: theme.spacing(0),
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  overflow: 'hidden',
}));

const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  '& h2': {
    margin: 0,
    fontSize: '20px',
    fontWeight: 600,
    color: theme.palette.text.primary,
  },
}));

const StyledGrid = styled(Grid)(({ theme }) => ({
  '& .MuiGrid-item': {
    paddingTop: theme.spacing(2),
    paddingLeft: theme.spacing(2),
  },
}));

/**
 * K6 Converter Page Component
 * Allows users to convert Postman collections to K6 load testing scripts
 */
export const K6ConverterPage: React.FC = () => {
  // Collection state
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('');

  // Notification state
  const [showSnackbar, setShowSnackbar] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Custom hooks
  const { collections, loading, error, fetchCollectionById } = useCollections();
  const {
    selectedCollection,
    folders,
    selectedFolders,
    k6Scripts,
    activeScriptIndex,
    isGenerating,
    configExpanded,
    config,
    setActiveScriptIndex,
    handleFolderSelect,
    handleConfigChange,
    handleCheckboxChange,
    handleSelectModeChange,
    setConfigExpanded,
    generateK6Script,
  } = useK6Converter({ selectedCollectionId, fetchCollectionById });

  // Event handlers
  const handleCollectionChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedCollectionId(event.target.value as string);
  };

  const handleCopyToClipboard = async (content: string) => {
    const result = await copyToClipboard(content);
    setSnackbarMessage(result.message);
    setShowSnackbar(true);
  };

  const handleSnackbarClose = () => {
    setShowSnackbar(false);
  };

  // Loading and error states
  if (loading) {
    return <EnhancedLoading message="Loading collections..." />;
  }

  if (error) {
    return <ErrorPanel error={error} />;
  }

  return (
    <StyledContainer>
      <StyledGrid container spacing={3}>
        {/* Header */}
        <Grid item xs={12}>
          <K6ConverterHeader />
        </Grid>

        {collections && collections.length > 0 ? (
          <>
            {/* Left panel - Collection selection and configuration */}
            <Grid item xs={12} md={4}>
              <StyledPaper>
                <Box p={3}>
                  <CollectionSelector
                    collections={collections}
                    selectedCollectionId={selectedCollectionId}
                    onCollectionChange={handleCollectionChange}
                  />

                  {selectedCollection && (
                    <>
                      <FolderSelector
                        folders={folders}
                        selectedFolders={selectedFolders}
                        selectionMode={config.selectionMode}
                        onFolderSelect={handleFolderSelect}
                        onSelectionModeChange={handleSelectModeChange}
                      />

                      <ConfigurationPanel
                        expanded={configExpanded}
                        onExpandChange={() => setConfigExpanded(!configExpanded)}
                        vus={config.vus}
                        duration={config.duration}
                        includeChecks={config.includeChecks}
                        includeSleep={config.includeSleep}
                        onConfigChange={handleConfigChange}
                        onCheckboxChange={handleCheckboxChange}
                      />

                      <GenerateScriptButton
                        isGenerating={isGenerating}
                        onClick={generateK6Script}
                      />
                    </>
                  )}
                </Box>
              </StyledPaper>
            </Grid>

            {/* Right panel - Generated scripts */}
            <Grid item xs={12} md={8}>
              <StyledPaper>
                <Box p={3}>
                  <SectionHeader>
                    <K6Icon sx={{ mr: 1, color: 'primary.main' }} />
                    <h2>Generated K6 Scripts</h2>
                  </SectionHeader>
                  <ScriptOutputPanel
                    scripts={k6Scripts}
                    activeScriptIndex={activeScriptIndex}
                    onTabChange={setActiveScriptIndex}
                    onCopyToClipboard={handleCopyToClipboard}
                  />
                </Box>
              </StyledPaper>
            </Grid>
          </>
        ) : (
          <Grid item xs={12}>
            <EnhancedEmptyState
              icon={K6Icon}
              title="No Collections Available"
              description="You need to create Postman collections first before you can convert them to K6 scripts. Create a collection to get started with performance testing."
              actionLabel="Create Collection"
              onAction={() => window.location.href = '/postman-converter/new'}
              actionIcon={AddIcon}
            />
          </Grid>
        )}
      </StyledGrid>

      {/* Notifications */}
      <Snackbar
        open={showSnackbar}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity="success">
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </StyledContainer>
  );
};
