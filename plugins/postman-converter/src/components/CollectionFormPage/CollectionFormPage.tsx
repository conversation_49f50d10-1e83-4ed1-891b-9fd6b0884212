import React, { useState } from 'react';
import {
  Content,
} from '@backstage/core-components';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';

// Icons
import CollectionIcon from '@mui/icons-material/CollectionsBookmark';
import UploadIcon from '@mui/icons-material/Upload';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import DescriptionIcon from '@mui/icons-material/Description';
import CodeIcon from '@mui/icons-material/Code';

// Internal APIs and Theme
import { postmanConverterApiRef } from '../../api';
import {
  getContainerStyles,
  getElevatedCardStyles,
  getButtonStyles,
  getFormFieldStyles,
  getEnhancedSpacing,
  getEnhancedBorderRadius,
  getEnhancedShadows,
  getEnhancedGradients,
} from '../../theme';

// Styled components for enhanced design
const StyledContainer = styled(Box)(({ theme }) => ({
  ...getContainerStyles(theme),
}));

const HeaderCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  marginBottom: getEnhancedSpacing(theme).xl,
}));

const FormCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  marginBottom: getEnhancedSpacing(theme).lg,
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  ...getFormFieldStyles(theme),
  marginBottom: getEnhancedSpacing(theme).lg,
}));

const ActionButton = styled(Button)(({ theme }) => ({
  ...getButtonStyles(theme),
  minWidth: '140px',
}));

const UploadButton = styled(Button)<{ component?: React.ElementType }>(({ theme }) => ({
  ...getButtonStyles(theme),
  borderStyle: 'dashed',
  borderWidth: '2px',
  padding: theme.spacing(2, 3),
  '&:hover': {
    borderStyle: 'dashed',
    borderWidth: '2px',
    transform: 'translateY(-2px)',
    boxShadow: getEnhancedShadows(theme).buttonHover,
  },
}));

const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: getEnhancedSpacing(theme).lg,
  '& h2': {
    margin: 0,
    fontSize: '24px',
    fontWeight: 600,
    color: theme.palette.text.primary,
  },
}));

const FormSection = styled(Box)(({ theme }) => ({
  marginBottom: getEnhancedSpacing(theme).xl,
}));

const ActionsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: getEnhancedSpacing(theme).md,
  justifyContent: 'flex-end',
  marginTop: getEnhancedSpacing(theme).xl,
  paddingTop: getEnhancedSpacing(theme).lg,
  borderTop: `1px solid ${theme.palette.divider}`,
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    '& button': {
      width: '100%',
    },
  },
}));

const InstructionsCard = styled(Card)(({ theme }) => ({
  backgroundColor: getEnhancedGradients(theme).primarySubtle,
  border: `1px solid ${theme.palette.primary.main}25`,
  borderRadius: getEnhancedBorderRadius(theme).medium,
  marginTop: getEnhancedSpacing(theme).md,
}));

export const CollectionFormPage = () => {
  const navigate = useNavigate();
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  const [formState, setFormState] = useState({
    name: '',
    description: '',
    content: '{}',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formState.name.trim()) {
      newErrors.name = 'Name is required';
    }

    try {
      const contentJson = JSON.parse(formState.content);

      // Validate Postman collection format
      const isValidPostmanCollection = (
        // Check for v2.1 format
        (// Check for older format
        ((contentJson.info && contentJson.info.name && Array.isArray(contentJson.item)) || (contentJson.name && (Array.isArray(contentJson.requests) || Array.isArray(contentJson.folders)))))
      );

      if (!isValidPostmanCollection) {
        newErrors.content = 'Invalid Postman collection format. Please export from Postman using Collection v2.1 format.';
      }
    } catch (e) {
      newErrors.content = 'Invalid JSON format. Please ensure you\'ve copied the entire collection JSON.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const collection = await postmanConverterApi.createCollection(formState);
      navigate(`/postman-converter/${collection.id}`);
    } catch (e) {
      errorApi.post(e as Error);
      setIsSubmitting(false);
    }
  };

  const handleChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormState({
      ...formState,
      [field]: event.target.value,
    });
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        // Try to parse to validate JSON
        JSON.parse(content);

        // If the file name doesn't have an extension, use it as the collection name
        if (file.name && !formState.name && file.name.endsWith('.json')) {
          const nameWithoutExtension = file.name.replace(/\.json$/, '');
          setFormState({
            ...formState,
            name: nameWithoutExtension,
            content,
          });
        } else {
          setFormState({
            ...formState,
            content,
          });
        }
      } catch (error) {
        setErrors({
          ...errors,
          content: 'Invalid JSON file. Please ensure you\'ve exported a valid Postman collection.',
        });
      }
    };
    reader.readAsText(file);
  };

  const handleCancel = () => {
    navigate('/postman-converter');
  };

  return (
    <Content>
      <StyledContainer>
        {/* Header Section */}
        <HeaderCard>
          <CardContent>
            <SectionHeader>
              <CollectionIcon sx={{ mr: 2, color: 'primary.main', fontSize: '32px' }} />
              <Box>
                <Typography component="h2" variant="h4" sx={{ m: 0, fontSize: '28px', fontWeight: 600 }}>
                  Add Postman Collection
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Upload a new Postman collection to start testing and converting your APIs
                </Typography>
              </Box>
            </SectionHeader>
          </CardContent>
        </HeaderCard>

        {/* Form Section */}
        <FormCard>
          <CardContent>
            <form onSubmit={handleSubmit}>
              {/* Basic Information Section */}
              <FormSection>
                <SectionHeader>
                  <DescriptionIcon sx={{ mr: 2, color: 'primary.main' }} />
                  <Typography component="h3" variant="h6">
                    Basic Information
                  </Typography>
                </SectionHeader>

                <StyledTextField
                  required
                  fullWidth
                  label="Collection Name"
                  value={formState.name}
                  onChange={handleChange('name')}
                  error={!!errors.name}
                  helperText={errors.name || "Enter a descriptive name for your collection"}
                />

                <StyledTextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={4}
                  value={formState.description}
                  onChange={handleChange('description')}
                  helperText="Optional description to help identify this collection"
                />
              </FormSection>

              {/* Collection Content Section */}
              <FormSection>
                <SectionHeader>
                  <CodeIcon sx={{ mr: 2, color: 'primary.main' }} />
                  <Typography component="h3" variant="h6">
                    Collection Content
                  </Typography>
                </SectionHeader>

                {/* Upload Section */}
                <Box mb={3}>
                  <input
                    accept=".json"
                    style={{ display: 'none' }}
                    id="collection-file-upload"
                    type="file"
                    onChange={handleFileUpload}
                  />
                  <label htmlFor="collection-file-upload">
                    <UploadButton
                      variant="outlined"
                      component="span"
                      color="primary"
                      startIcon={<UploadIcon />}
                      fullWidth
                    >
                      Upload Postman Collection File
                    </UploadButton>
                  </label>
                </Box>

                {/* JSON Content */}
                <StyledTextField
                  required
                  fullWidth
                  label="Collection Content (JSON)"
                  multiline
                  rows={12}
                  value={formState.content}
                  onChange={handleChange('content')}
                  error={!!errors.content}
                  helperText={errors.content || "Paste the exported Postman collection JSON here or use the upload button above"}
                  variant="outlined"
                  sx={{ fontFamily: 'monospace' }}
                />

                {/* Instructions Card */}
                <InstructionsCard>
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                      How to export from Postman:
                    </Typography>
                    <Box component="ol" sx={{ pl: 2, m: 0 }}>
                      <Typography component="li" variant="body2">
                        In Postman, click on the collection you want to export
                      </Typography>
                      <Typography component="li" variant="body2">
                        Click the "..." (three dots) menu
                      </Typography>
                      <Typography component="li" variant="body2">
                        Select "Export"
                      </Typography>
                      <Typography component="li" variant="body2">
                        Choose "Collection v2.1" format (recommended)
                      </Typography>
                      <Typography component="li" variant="body2">
                        Save the file and upload it using the button above, or copy the JSON content and paste it in the text area
                      </Typography>
                    </Box>
                  </CardContent>
                </InstructionsCard>
              </FormSection>

              {/* Actions */}
              <ActionsContainer>
                <ActionButton
                  type="button"
                  variant="outlined"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                  startIcon={<CancelIcon />}
                >
                  Cancel
                </ActionButton>
                <ActionButton
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={isSubmitting}
                  startIcon={<SaveIcon />}
                >
                  {isSubmitting ? 'Saving...' : 'Save Collection'}
                </ActionButton>
              </ActionsContainer>
            </form>
          </CardContent>
        </FormCard>
      </StyledContainer>
    </Content>
  );
};
