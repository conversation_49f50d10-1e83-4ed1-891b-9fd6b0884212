import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Content,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Divider from '@mui/material/Divider';
import Chip from '@mui/material/Chip';
import LinearProgress from '@mui/material/LinearProgress';
import { styled } from '@mui/material/styles';
import { useAsync } from 'react-use';
import { postmanConverterApiRef } from '../../api';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import UploadIcon from '@mui/icons-material/Upload';
import CollectionIcon from '@mui/icons-material/CollectionsBookmark';
import DescriptionIcon from '@mui/icons-material/Description';
import CodeIcon from '@mui/icons-material/Code';
import { EnhancedLoading } from '../common';
import {
  getContainerStyles,
  getElevatedCardStyles,
  getButtonStyles,
  getFormFieldStyles,
  getEnhancedSpacing,
  getEnhancedBorderRadius,
  getEnhancedShadows,
  getEnhancedGradients,
} from '../../theme';

// Styled components for enhanced design
const StyledContainer = styled(Box)(({ theme }) => ({
  ...getContainerStyles(theme),
}));

const HeaderCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  marginBottom: getEnhancedSpacing(theme).xl,
}));

const FormCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  marginBottom: getEnhancedSpacing(theme).lg,
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  ...getFormFieldStyles(theme),
  marginBottom: getEnhancedSpacing(theme).lg,
}));

const ActionButton = styled(Button)(({ theme }) => ({
  ...getButtonStyles(theme),
  minWidth: '140px',
}));

const UploadButton = styled(Button)(({ theme }) => ({
  ...getButtonStyles(theme),
  borderStyle: 'dashed',
  borderWidth: '2px',
  padding: theme.spacing(2, 3),
  '&:hover': {
    borderStyle: 'dashed',
    borderWidth: '2px',
    transform: 'translateY(-2px)',
    boxShadow: getEnhancedShadows(theme).buttonHover,
  },
}));

const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: getEnhancedSpacing(theme).lg,
  '& h2': {
    margin: 0,
    fontSize: '24px',
    fontWeight: 600,
    color: theme.palette.text.primary,
  },
}));

const FormSection = styled(Box)(({ theme }) => ({
  marginBottom: getEnhancedSpacing(theme).xl,
}));

const ActionsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: getEnhancedSpacing(theme).md,
  justifyContent: 'flex-end',
  marginTop: getEnhancedSpacing(theme).xl,
  paddingTop: getEnhancedSpacing(theme).lg,
  borderTop: `1px solid ${theme.palette.divider}`,
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    '& button': {
      width: '100%',
    },
  },
}));

const InstructionsCard = styled(Card)(({ theme }) => ({
  backgroundColor: getEnhancedGradients(theme).primarySubtle,
  border: `1px solid ${theme.palette.primary.main}25`,
  borderRadius: getEnhancedBorderRadius(theme).medium,
  marginTop: getEnhancedSpacing(theme).md,
}));

const ProgressContainer = styled(Box)(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  zIndex: theme.zIndex.appBar + 1,
}));

export const CollectionEditPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);
  
  const [formState, setFormState] = useState({
    name: '',
    description: '',
    content: '{}',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch the collection data
  const { loading, error } = useAsync(async () => {
    if (!id) return undefined;

    try {
      setIsLoading(true);
      const collection = await postmanConverterApi.getCollectionById(id);
      setFormState({
        name: collection.name,
        description: collection.description || '',
        content: collection.content || '{}',
      });
      return collection;
    } catch (e) {
      errorApi.post(e as any);
      throw e;
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formState.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    try {
      const contentJson = JSON.parse(formState.content);
      
      // Validate Postman collection format
      const isValidPostmanCollection = (
        // Check for v2.1 format
        (// Check for older format
        ((contentJson.info && contentJson.info.name && Array.isArray(contentJson.item)) || (contentJson.name && (Array.isArray(contentJson.requests) || Array.isArray(contentJson.folders)))))
      );
      
      if (!isValidPostmanCollection) {
        newErrors.content = 'Invalid Postman collection format. Please export from Postman using Collection v2.1 format.';
      }
    } catch (e) {
      newErrors.content = 'Invalid JSON format. Please ensure you\'ve copied the entire collection JSON.';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm() || !id) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await postmanConverterApi.updateCollection(id, formState);
      navigate(`/postman-converter/${id}`);
    } catch (e) {
      errorApi.post(e as any);
      setIsSubmitting(false);
    }
  };

  const handleChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormState({
      ...formState,
      [field]: event.target.value,
    });
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        // Try to parse to validate JSON
        JSON.parse(content);
        
        setFormState({
          ...formState,
          content,
        });
      } catch (error) {
        setErrors({
          ...errors,
          content: 'Invalid JSON file. Please ensure you\'ve exported a valid Postman collection.',
        });
      }
    };
    reader.readAsText(file);
  };

  const handleCancel = () => {
    navigate(`/postman-converter/${id}`);
  };

  if (loading || isLoading) {
    return <EnhancedLoading message="Loading collection for editing..." />;
  }

  if (error) {
    return <ErrorPanel error={error} />;
  }

  return (
    <Content>
      {isSubmitting && (
        <ProgressContainer>
          <LinearProgress />
        </ProgressContainer>
      )}

      <StyledContainer>
        {/* Header Section */}
        <HeaderCard>
          <CardContent sx={{ p: 4 }}>
            <Box display="flex" alignItems="center" mb={2}>
              <EditIcon sx={{ mr: 2, color: 'primary.main', fontSize: '40px' }} />
              <Typography
                variant="h3"
                component="h1"
                fontWeight="bold"
                sx={{ lineHeight: 1.2 }}
              >
                Edit Collection
              </Typography>
            </Box>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ fontSize: '17.6px', lineHeight: 1.6 }}
            >
              Update your Postman collection details and content. Make sure to validate your JSON before saving.
            </Typography>
          </CardContent>
        </HeaderCard>

        {/* Form Section */}
        <form onSubmit={handleSubmit}>
          {/* Basic Information */}
          <FormCard>
            <CardContent sx={{ p: 4 }}>
              <SectionHeader>
                <CollectionIcon sx={{ mr: 2, color: 'primary.main', fontSize: '24px' }} />
                <h2>Basic Information</h2>
              </SectionHeader>

              <FormSection>
                <StyledTextField
                  required
                  fullWidth
                  label="Collection Name"
                  value={formState.name}
                  onChange={handleChange('name')}
                  error={!!errors.name}
                  helperText={errors.name || 'Enter a descriptive name for your collection'}
                  variant="outlined"
                />

                <StyledTextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={4}
                  value={formState.description}
                  onChange={handleChange('description')}
                  helperText="Optional description to help others understand this collection"
                  variant="outlined"
                />
              </FormSection>
            </CardContent>
          </FormCard>

          {/* Collection Content */}
          <FormCard>
            <CardContent sx={{ p: 4 }}>
              <SectionHeader>
                <CodeIcon sx={{ mr: 2, color: 'secondary.main', fontSize: '24px' }} />
                <h2>Collection Content</h2>
              </SectionHeader>

              <FormSection>
                {/* Upload Section */}
                <Box mb={3}>
                  <input
                    accept=".json"
                    style={{ display: 'none' }}
                    id="collection-file-upload"
                    type="file"
                    onChange={handleFileUpload}
                  />
                  <label htmlFor="collection-file-upload">
                    <UploadButton
                      variant="outlined"
                      component="span"
                      color="primary"
                      startIcon={<UploadIcon />}
                      fullWidth
                    >
                      Upload New Postman Collection File
                    </UploadButton>
                  </label>
                </Box>

                {/* JSON Content */}
                <StyledTextField
                  required
                  fullWidth
                  label="Collection Content (JSON)"
                  multiline
                  rows={12}
                  value={formState.content}
                  onChange={handleChange('content')}
                  error={!!errors.content}
                  helperText={errors.content || "Paste the exported Postman collection JSON here or use the upload button above"}
                  variant="outlined"
                  sx={{ fontFamily: 'monospace' }}
                />

                {/* Instructions */}
                <InstructionsCard>
                  <CardContent sx={{ p: 3 }}>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} />
                      How to Export from Postman
                    </Typography>
                    <Box component="ol" sx={{ pl: 2, m: 0 }}>
                      <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                        In Postman, click on the collection you want to export
                      </Typography>
                      <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                        Click the "..." (three dots) menu next to the collection name
                      </Typography>
                      <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                        Select "Export" from the dropdown menu
                      </Typography>
                      <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                        Choose "Collection v2.1" format (recommended)
                      </Typography>
                      <Typography component="li" variant="body2">
                        Save the file and upload it using the button above, or copy the JSON content and paste it in the text area
                      </Typography>
                    </Box>
                  </CardContent>
                </InstructionsCard>
              </FormSection>
            </CardContent>
          </FormCard>

          {/* Actions */}
          <ActionsContainer>
            <ActionButton
              type="button"
              variant="outlined"
              onClick={handleCancel}
              disabled={isSubmitting}
              startIcon={<CancelIcon />}
            >
              Cancel
            </ActionButton>
            <ActionButton
              type="submit"
              variant="contained"
              color="primary"
              disabled={isSubmitting}
              startIcon={<SaveIcon />}
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </ActionButton>
          </ActionsContainer>
        </form>
      </StyledContainer>
    </Content>
  );
};
