import React from 'react';

// Backstage Core Components
import {
  Table,
  TableColumn,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi, configApiRef, errorApiRef } from '@backstage/core-plugin-api';

// MUI Core Components
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Chip from '@mui/material/Chip';

// MUI Styles
import { styled } from '@mui/material/styles';

// MUI Icons
import AddIcon from '@mui/icons-material/Add';
import CollectionIcon from '@mui/icons-material/CollectionsBookmark';

// React Router
import { Link as RouterLink } from 'react-router-dom';

// Third-party Libraries
import { useAsync } from 'react-use';

// Internal Components & APIs
import { postmanConverterApiRef } from '../../api';
import { Collection } from '../../types';
import { EnhancedLoading, EnhancedEmptyState } from '../common';

const StyledContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(0),
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(3),
  padding: theme.spacing(2, 0),
}));

const StyledTable = styled(Paper)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(3),
  padding: theme.spacing(1.5, 3),
  textTransform: 'none',
  fontWeight: theme.typography.fontWeightMedium,
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.15)',
  },
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
}));

const CollectionLink = styled(RouterLink)(({ theme }) => ({
  textDecoration: 'none',
  color: theme.palette.primary.main,
  fontWeight: theme.typography.fontWeightMedium,
  '&:hover': {
    textDecoration: 'underline',
  },
}));

export const CollectionListPage = () => {
  const configApi = useApi(configApiRef);
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  const { value: collections, loading, error } = useAsync(async () => {
    try {
      return await postmanConverterApi.getCollections();
    } catch (e) {
      errorApi.post(e);
      throw e;
    }
  }, []);

  const columns: TableColumn<Collection>[] = [
    {
      title: 'Name',
      field: 'name',
      highlight: true,
      render: (row: Collection) => (
        <Box display="flex" alignItems="center">
          <CollectionIcon
            sx={{ mr: 1, color: 'primary.main', fontSize: '19.2px' }}
          />
          <CollectionLink to={`/postman-converter/${row.id}`}>
            {row.name}
          </CollectionLink>
        </Box>
      ),
    },
    {
      title: 'Description',
      field: 'description',
      render: (row: Collection) => (
        <Box sx={{ maxWidth: '300px' }}>
          {row.description || (
            <Chip
              label="No description"
              size="small"
              variant="outlined"
              sx={{ opacity: 0.6 }}
            />
          )}
        </Box>
      ),
    },
    {
      title: 'Created',
      field: 'created_at',
      render: (row: Collection) => (
        <Chip
          label={new Date(row.created_at).toLocaleDateString()}
          size="small"
          variant="outlined"
          color="primary"
        />
      ),
    },
    {
      title: 'Updated',
      field: 'updated_at',
      render: (row: Collection) => (
        <Chip
          label={new Date(row.updated_at).toLocaleDateString()}
          size="small"
          variant="filled"
          color="secondary"
        />
      ),
    },
  ];

  if (loading) {
    return <EnhancedLoading message="Loading collections..." />;
  }

  if (error) {
    return <ErrorPanel error={error} />;
  }

  return (
    <StyledContainer>
      <HeaderSection>
        <Box>
          <Box display="flex" alignItems="center" mb={1}>
            <CollectionIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Box component="h2" sx={{ m: 0, fontSize: '24px', fontWeight: 600 }}>
              Collections
            </Box>
          </Box>
          <Box component="p" sx={{ m: 0, color: 'text.secondary', fontSize: '14px' }}>
            Manage your Postman collections for testing and conversion
          </Box>
        </Box>
        <ActionButton
          component={RouterLink}
          to="/postman-converter/new"
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
        >
          Add Collection
        </ActionButton>
      </HeaderSection>

      {collections && collections.length > 0 ? (
        <StyledTable>
          <Table
            options={{
              paging: true,
              search: true,
              pageSize: 10,
              pageSizeOptions: [5, 10, 20],
              searchFieldStyle: { borderRadius: '8px' },
              headerStyle: {
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                fontWeight: 600,
              },
            }}
            data={collections}
            columns={columns}
            title=""
          />
        </StyledTable>
      ) : (
        <EnhancedEmptyState
          icon={CollectionIcon}
          title="No collections found"
          description="Get started by creating your first Postman collection. You can import existing collections or create new ones from scratch."
          actionLabel="Create Collection"
          onAction={() => window.location.href = '/postman-converter/new'}
          actionIcon={AddIcon}
        />
      )}
    </StyledContainer>
  );
};
