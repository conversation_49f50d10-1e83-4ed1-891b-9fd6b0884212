import { Collection } from '../../types';

/**
 * Collection list page state interface
 */
export interface CollectionListPageState {
  collections: Collection[];
  loading: boolean;
  error?: Error;
}

/**
 * Collection table column configuration
 */
export interface CollectionTableColumn {
  title: string;
  field: string;
  highlight?: boolean;
  render?: (row: Collection) => React.ReactNode;
}

/**
 * Collection list page handlers interface
 */
export interface CollectionListPageHandlers {
  handleRefresh: () => void;
  handleCreateNew: () => void;
  handleCollectionClick: (collection: Collection) => void;
}

/**
 * Collection list display props
 */
export interface CollectionListProps {
  collections: Collection[];
  loading: boolean;
  error?: Error;
  onRefresh: () => void;
}

/**
 * Collection table props
 */
export interface CollectionTableProps {
  collections: Collection[];
  columns: CollectionTableColumn[];
  loading: boolean;
}
