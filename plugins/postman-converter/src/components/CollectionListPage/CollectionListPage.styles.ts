import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { Link } from '@backstage/core-components';

import {
  getContainerStyles,
  getElevatedCardStyles,
  getButtonStyles,
  getEnhancedSpacing,
} from '../../theme';

// Container Styles
export const StyledContainer = styled(Box)(({ theme }) => ({
  ...getContainerStyles(theme),
}));

// Header Section Styles
export const HeaderSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  marginBottom: getEnhancedSpacing(theme).xl,
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: getEnhancedSpacing(theme).lg,
  },
}));

// Action Button Styles
export const ActionButton = styled(Button)(({ theme }) => ({
  ...getButtonStyles(theme),
}));

// Collection Link Styles
export const CollectionLink = styled(Link)(({ theme }) => ({
  textDecoration: 'none',
  color: theme.palette.text.primary,
  fontWeight: theme.typography.fontWeightMedium,
  '&:hover': {
    color: theme.palette.primary.main,
    textDecoration: 'underline',
  },
}));
