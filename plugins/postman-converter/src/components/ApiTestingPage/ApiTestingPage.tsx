import { useState, useCallback, useEffect } from 'react';
import { SelectChangeEvent } from '@mui/material/Select';
import { HttpMethod } from '../../types';

// Hooks
import { useCollections } from './hooks/useCollections';
import { useRequest } from './hooks/useRequest';
import { useEnvironments } from './hooks/useEnvironments';
import { useNotifications } from './hooks/useNotifications';
import { useDeleteOperations } from './hooks/useDeleteOperations';
import { useContextMenu } from './hooks/useContextMenu';

// Components
import { ApiTestingLayout } from './components/ApiTestingLayout';
import { DialogManager } from './components/DialogManager';
import { ContextMenuComponent } from './components/ContextMenuComponent';
import { NotificationSnackbar } from './components/NotificationSnackbar';



export const ApiTestingPage = () => {
  // Collections management
  const {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    unsavedCollections,
    isSaving,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleImportCollection,
    handleAddFolder,
    handleAddRequest,
    handleRenameCollection,
    handleRenameFolder,
    handleRenameRequest,
    handleSaveCollection,
    markCollectionAsUnsaved,
  } = useCollections();

  // Handle save all collections
  const handleSaveAllCollections = useCallback(async () => {
    const savePromises = Array.from(unsavedCollections).map(collectionId =>
      handleSaveCollection(collectionId)
    );
    await Promise.all(savePromises);
  }, [unsavedCollections, handleSaveCollection]);

  // Request management
  const {
    currentRequest,
    setCurrentRequest,
    currentResponse,
    isLoading,
    tabValue,
    responseTabValue,
    isGeneratingTests,
    isRunningTests,
    testResults,
    testError,
    isSavingPreRequestScript,
    preRequestScriptError,
    handleTabChange,
    handleResponseTabChange,
    handleMethodChange,
    handleUrlChange,
    handleRequestChange,
    handleSendRequest,
    handleGenerateTests,
    handleRunTests,
    handleSaveTests,
    handleSavePreRequestScript,
  } = useRequest(collections, setCollections, markCollectionAsUnsaved);

  // Environment management
  const {
    environments,
    setEnvironments,
    currentEnvironment,
    setCurrentEnvironment,
    isEnvironmentDialogOpen,
    setIsEnvironmentDialogOpen,
    handleImportEnvironment,
  } = useEnvironments();

  // Notifications
  const { snackbar, hideNotification } = useNotifications();

  // Dialog states
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameDialogData, setRenameDialogData] = useState<{
    itemType: 'collection' | 'folder' | 'request';
    itemId: string;
    collectionId: string;
    currentName: string;
  } | null>(null);
  const [selectedCollectionForAction, setSelectedCollectionForAction] = useState<string>('');
  const [selectedFolderForAction, setSelectedFolderForAction] = useState<string>('');
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string>(environments[0]?.id || '');

  // Delete operations hook
  const { handleDeleteFolder, handleDeleteRequest } = useDeleteOperations({
    collections,
    setCollections,
    selectedItemId: selectedItemId || '',
    handleItemSelect,
    setCurrentRequest,
  });

  // Context menu hook
  const {
    contextMenu,
    handleContextMenu,
    handleContextMenuClose,
    handleAddFolderFromContext,
    handleAddRequestFromContext,
    handleRenameFromContext,
    handleDeleteFolderFromContext,
    handleDeleteRequestFromContext,
  } = useContextMenu({
    collections,
    setSelectedCollectionForAction,
    setSelectedFolderForAction,
    setCreateFolderDialogOpen,
    setCreateRequestDialogOpen,
    setRenameDialogOpen,
    setRenameDialogData,
    handleDeleteFolder,
    handleDeleteRequest,
  });

  // Handle environment change
  const handleEnvironmentChange = useCallback((event: SelectChangeEvent<string>) => {
    setCurrentEnvironment(event.target.value as string);
  }, [setCurrentEnvironment]);

  // Handle send request with environment
  const handleSendRequestWithEnvironment = useCallback(async () => {
    const currentEnv = environments.find(env => env.id === currentEnvironment);
    return handleSendRequest(currentEnv);
  }, [handleSendRequest, environments, currentEnvironment]);

  // Wrapper functions for save operations
  const handleSavePreRequestScriptWrapper = useCallback(() => {
    if (currentRequest?.preRequestScript !== undefined) {
      handleSavePreRequestScript(currentRequest.preRequestScript);
    }
  }, [handleSavePreRequestScript, currentRequest?.preRequestScript]);

  const handleSaveTestsWrapper = useCallback(() => {
    if (currentRequest?.testScript !== undefined) {
      handleSaveTests(currentRequest.testScript);
    }
  }, [handleSaveTests, currentRequest?.testScript]);

  // Load selected request when item is selected
  useEffect(() => {
    if (selectedItemId) {
      const collection = collections.find(col => {
        return Object.keys(col.requests).includes(selectedItemId);
      });

      if (collection && collection.requests[selectedItemId]) {
        const selectedRequest = collection.requests[selectedItemId];

        setCurrentRequest(selectedRequest);
      }
    }
  }, [selectedItemId, collections, setCurrentRequest]);

  return (
    <>
      <ApiTestingLayout
        // Collections props
        collections={collections}
        collectionsLoading={collectionsLoading}
        collectionsError={collectionsError instanceof Error ? collectionsError.message : (collectionsError || null)}
        expandedFolders={expandedFolders}
        selectedItemId={selectedItemId}
        unsavedCollections={unsavedCollections}
        isSaving={isSaving}
        onFolderToggle={handleFolderToggle}
        onItemSelect={handleItemSelect}
        onContextMenu={handleContextMenu}
        onAddCollection={handleAddCollection}
        onSaveCollection={handleSaveCollection}
        onSaveAllCollections={handleSaveAllCollections}

        // Environment props
        environments={environments}
        currentEnvironment={currentEnvironment}
        selectedEnvironmentId={selectedEnvironmentId}
        environmentDialogOpen={isEnvironmentDialogOpen}
        onEnvironmentChange={handleEnvironmentChange}
        onOpenEnvironmentDialog={() => setIsEnvironmentDialogOpen(true)}
        onCloseEnvironmentDialog={() => setIsEnvironmentDialogOpen(false)}
        onOpenExportDialog={() => setExportDialogOpen(true)}
        onEnvironmentUpdate={setEnvironments}
        onSelectedEnvironmentChange={setSelectedEnvironmentId}

        // Request props
        currentRequest={currentRequest}
        currentResponse={currentResponse}
        isLoading={isLoading}
        tabValue={tabValue}
        responseTabValue={responseTabValue}
        isGeneratingTests={isGeneratingTests}
        isRunningTests={isRunningTests}
        testResults={testResults}
        testError={testError}
        isSavingPreRequestScript={isSavingPreRequestScript}
        preRequestScriptError={preRequestScriptError}
        onRequestChange={handleRequestChange}
        onMethodChange={handleMethodChange}
        onUrlChange={handleUrlChange}
        onTabChange={handleTabChange}
        onResponseTabChange={handleResponseTabChange}
        onSendRequest={handleSendRequestWithEnvironment}
        onSavePreRequestScript={handleSavePreRequestScriptWrapper}
        onSaveTests={handleSaveTestsWrapper}
        onRunTests={handleRunTests}
        onGenerateTests={handleGenerateTests}
      />

      <ContextMenuComponent
        contextMenu={contextMenu}
        onClose={handleContextMenuClose}
        onAddFolder={handleAddFolderFromContext}
        onAddRequest={handleAddRequestFromContext}
        onRename={handleRenameFromContext}
        onDeleteFolder={handleDeleteFolderFromContext}
        onDeleteRequest={handleDeleteRequestFromContext}
      />

      <DialogManager
        // Import Dialog
        importDialogOpen={importDialogOpen}
        onCloseImportDialog={() => setImportDialogOpen(false)}
        onImportCollection={handleImportCollection}
        onImportEnvironment={handleImportEnvironment}

        // Export Dialog
        exportDialogOpen={exportDialogOpen}
        onCloseExportDialog={() => setExportDialogOpen(false)}
        collections={collections}
        environments={environments}

        // Create Folder Dialog
        createFolderDialogOpen={createFolderDialogOpen}
        onCloseCreateFolderDialog={() => setCreateFolderDialogOpen(false)}
        onCreateFolder={(folderName, parentId, collectionId) => {
          handleAddFolder(collectionId, parentId || undefined, folderName);
        }}
        selectedCollectionForAction={selectedCollectionForAction}
        selectedFolderForAction={selectedFolderForAction}

        // Create Request Dialog
        createRequestDialogOpen={createRequestDialogOpen}
        onCloseCreateRequestDialog={() => setCreateRequestDialogOpen(false)}
        onCreateRequest={(requestName, method, url, parentId, collectionId) => {
          handleAddRequest(collectionId, parentId || undefined, requestName, method as HttpMethod, url);
        }}

        // Rename Dialog
        renameDialogOpen={renameDialogOpen}
        onCloseRenameDialog={() => {
          setRenameDialogOpen(false);
          setRenameDialogData(null);
        }}
        onRename={(newName) => {
          if (renameDialogData) {
            if (renameDialogData.itemType === 'collection') {
              handleRenameCollection(renameDialogData.itemId, newName);
            } else if (renameDialogData.itemType === 'folder') {
              handleRenameFolder(renameDialogData.collectionId, renameDialogData.itemId, newName);
            } else if (renameDialogData.itemType === 'request') {
              handleRenameRequest(renameDialogData.collectionId, renameDialogData.itemId, newName);
            }
          }
        }}
        renameDialogData={renameDialogData}
      />

      <NotificationSnackbar
        snackbar={snackbar}
        onClose={hideNotification}
      />
    </>
  );
};