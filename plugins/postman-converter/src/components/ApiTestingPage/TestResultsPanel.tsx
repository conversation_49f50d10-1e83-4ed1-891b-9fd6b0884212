import React from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Divider from '@mui/material/Divider';
import Collapse from '@mui/material/Collapse';
import IconButton from '@mui/material/IconButton';
import makeStyles from '@mui/styles/makeStyles';
import Alert from '@mui/material/Alert';

// Icons
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import TimerIcon from '@mui/icons-material/Timer';

export interface TestResult {
  id: string;
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(2),
  },
  summary: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  passed: {
    color: theme.palette.success.main,
  },
  failed: {
    color: theme.palette.error.main,
  },
  neutral: {
    color: theme.palette.text.secondary,
  },
  listItem: {
    borderLeft: `4px solid ${theme.palette.background.paper}`,
    '&.passed': {
      borderLeftColor: theme.palette.success.main,
    },
    '&.failed': {
      borderLeftColor: theme.palette.error.main,
    },
  },
  duration: {
    display: 'flex',
    alignItems: 'center',
    color: theme.palette.text.secondary,
    fontSize: '14px',
  },
  durationIcon: {
    fontSize: '16px',
    marginRight: theme.spacing(0.5),
  },
  errorDetails: {
    padding: theme.spacing(1, 2, 1, 6),
    backgroundColor: theme.palette.error.light,
    color: theme.palette.error.contrastText,
    borderRadius: theme.shape.borderRadius,
    whiteSpace: 'pre-wrap',
    fontFamily: 'monospace',
    fontSize: '14px',
  },
}));

interface TestResultsPanelProps {
  results: TestResult[];
  isRunning?: boolean;
  error?: string | null;
}

export const TestResultsPanel: React.FC<TestResultsPanelProps> = ({
  results,
  isRunning = false,
  error = null,
}) => {
  const classes = useStyles();
  const [expandedItems, setExpandedItems] = React.useState<Record<string, boolean>>({});

  const passedTests = results.filter(result => result.passed).length;
  const failedTests = results.length - passedTests;

  const toggleExpand = (id: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  if (isRunning) {
    return (
      <Paper className={classes.root}>
        <Typography variant="h6">Test Results</Typography>
        <Box display="flex" justifyContent="center" alignItems="center" height="100px">
          <Typography variant="body1">Running tests...</Typography>
        </Box>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper className={classes.root}>
        <Typography variant="h6">Test Results</Typography>
        <Alert severity="error" style={{ marginTop: '16px' }}>
          {error}
        </Alert>
      </Paper>
    );
  }

  if (results.length === 0) {
    return (
      <Paper className={classes.root}>
        <Typography variant="h6">Test Results</Typography>
        <Box display="flex" justifyContent="center" alignItems="center" height="100px">
          <Typography variant="body1" className={classes.neutral}>
            No test results available. Run tests to see results here.
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper className={classes.root}>
      <Typography variant="h6">Test Results</Typography>
      <Box className={classes.summary}>
        <Box display="flex" alignItems="center" mr={3}>
          <CheckCircleIcon className={classes.passed} style={{ marginRight: '8px' }} />
          <Typography variant="body1">
            {passedTests} passed
          </Typography>
        </Box>
        <Box display="flex" alignItems="center">
          <CancelIcon className={classes.failed} style={{ marginRight: '8px' }} />
          <Typography variant="body1">
            {failedTests} failed
          </Typography>
        </Box>
      </Box>
      <Divider />
      <List>
        {results.map((result) => (
          <React.Fragment key={result.id}>
            <ListItem 
              className={`${classes.listItem} ${result.passed ? 'passed' : 'failed'}`}
              button={!result.passed}
              onClick={() => !result.passed && toggleExpand(result.id)}
            >
              <ListItemIcon>
                {result.passed ? (
                  <CheckCircleIcon className={classes.passed} />
                ) : (
                  <CancelIcon className={classes.failed} />
                )}
              </ListItemIcon>
              <ListItemText 
                primary={result.name}
                secondary={
                  <span className={classes.duration}>
                    <TimerIcon className={classes.durationIcon} />
                    {result.duration}ms
                  </span>
                }
              />
              {!result.passed && result.error && (
                <IconButton edge="end" onClick={() => toggleExpand(result.id)} size="large">
                  {expandedItems[result.id] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )}
            </ListItem>
            
            {!result.passed && result.error && (
              <Collapse in={expandedItems[result.id]} timeout="auto" unmountOnExit>
                <Box className={classes.errorDetails}>
                  {result.error}
                </Box>
              </Collapse>
            )}
          </React.Fragment>
        ))}
      </List>
    </Paper>
  );
};
