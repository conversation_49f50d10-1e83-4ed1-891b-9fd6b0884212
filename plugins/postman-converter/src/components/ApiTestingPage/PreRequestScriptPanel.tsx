import React, { useState, useEffect } from 'react';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import CircularProgress from '@mui/material/CircularProgress';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import TextField from '@mui/material/TextField';
import makeStyles from '@mui/styles/makeStyles';
import Alert from '@mui/material/Alert';
import { CodeSnippet } from '@backstage/core-components';
import { ApiRequest } from '../../types';

// Icons
import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(2),
  },
  codeEditor: {
    width: '100%',
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
    '& .MuiOutlinedInput-root': {
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      fontSize: '14px',
      lineHeight: '1.5',
      '& textarea': {
        resize: 'vertical',
      },
    },
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: theme.spacing(2),
  },
  leftButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
  rightButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
}));

interface PreRequestScriptPanelProps {
  request: ApiRequest;
  onSaveScript: (script: string) => void;
  isSaving?: boolean;
  error?: string | null;
}

export const PreRequestScriptPanel: React.FC<PreRequestScriptPanelProps> = ({
  request,
  onSaveScript,
  isSaving = false,
  error = null,
}) => {
  const classes = useStyles();
  const [script, setScript] = useState<string>('');
  const [isEdited, setIsEdited] = useState<boolean>(false);

  // Update script when request changes
  useEffect(() => {
    if (request.id && request.preRequestScript) {
      setScript(request.preRequestScript);
      setIsEdited(false);
    } else {
      setScript('');
      setIsEdited(false);
    }
  }, [request.id, request.preRequestScript]);

  const handleSaveScript = () => {
    onSaveScript(script);
    setIsEdited(false);
  };

  const handleClearScript = () => {
    setScript('');
    setIsEdited(true);
  };

  const handleScriptChange = (newScript: string) => {
    setScript(newScript);
    setIsEdited(true);
  };

  return (
    <Paper className={classes.root}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">Pre-request Script</Typography>
      </Box>
      <Typography variant="body2" color="textSecondary" paragraph>
        Write scripts to execute before the request is sent
      </Typography>
      {error && (
        <Alert severity="error" style={{ marginBottom: '16px' }}>
          {error}
        </Alert>
      )}
      <Box className={classes.codeEditor}>
        <TextField
          multiline
          rows={10}
          variant="outlined"
          fullWidth
          value={script}
          onChange={(e) => handleScriptChange(e.target.value)}
          placeholder="// Write your pre-request scripts here
// Example:
pm.globals.set('timestamp', Date.now());
pm.environment.set('userId', '12345');

// Set dynamic variables
const randomId = Math.floor(Math.random() * 1000);
pm.environment.set('randomId', randomId);"
          InputProps={{
            style: {
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: '14px',
              lineHeight: '1.5',
            },
          }}
        />
      </Box>
      <Box className={classes.buttonContainer}>
        <div className={classes.leftButtons}>
          <Button
            variant="outlined"
            onClick={handleClearScript}
            disabled={!script}
            startIcon={<DeleteIcon />}>
            Clear
          </Button>
        </div>
        <div className={classes.rightButtons}>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleSaveScript}
            disabled={!isEdited || isSaving}
            startIcon={isSaving ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </Box>
    </Paper>
  );
};
