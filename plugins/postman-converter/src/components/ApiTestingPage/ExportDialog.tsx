import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import CircularProgress from '@mui/material/CircularProgress';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Alert from '@mui/material/Alert';
import { useApi } from '@backstage/core-plugin-api';
import { ImportExportService } from '../../services/ImportExportService';
import { ApiCollection, ApiEnvironment } from '../../types';
import { postmanConverterApiRef } from '../../api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`export-tabpanel-${index}`}
      aria-labelledby={`export-tab-${index}`}
      {...other}
    >
      {value === index && <Box p={2}>{children}</Box>}
    </div>
  );
};

interface ExportDialogProps {
  open: boolean;
  onClose: () => void;
  collections: ApiCollection[];
  environments: ApiEnvironment[];
}

export const ExportDialog: React.FC<ExportDialogProps> = ({
  open,
  onClose,
  collections,
  environments,
}) => {
  const postmanConverterApi = useApi(postmanConverterApiRef);
  const [tabValue, setTabValue] = useState(0);
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('');
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string>('');
  const [exportFormat, setExportFormat] = useState<'json' | 'file'>('json');
  const [exportedData, setExportedData] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset selected IDs when collections or environments change
  useEffect(() => {
    if (collections.length > 0 && !collections.find(c => c.id === selectedCollectionId)) {
      setSelectedCollectionId(collections[0].id);
    }
  }, [collections, selectedCollectionId]);

  useEffect(() => {
    if (environments.length > 0 && !environments.find(e => e.id === selectedEnvironmentId)) {
      setSelectedEnvironmentId(environments[0].id);
    }
  }, [environments, selectedEnvironmentId]);

  const handleTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
    setExportedData('');
    setError(null);
  };

  const handleExportFormatChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setExportFormat(event.target.value as 'json' | 'file');
  };

  const handleCollectionChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedCollectionId(event.target.value as string);
    setExportedData('');
  };

  const handleEnvironmentChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedEnvironmentId(event.target.value as string);
    setExportedData('');
  };

  const handleExport = async () => {
    setIsLoading(true);
    setError(null);
    setExportedData('');

    try {
      if (tabValue === 0) {
        // Export collection - fetch latest from database to ensure we have current data
        const collection = collections.find(c => c.id === selectedCollectionId);
        if (!collection) {
          throw new Error('Please select a collection to export');
        }

        // Fetch the latest collection content from the database
        const latestCollection = await postmanConverterApi.getCollectionById(selectedCollectionId);

        // Parse the latest content to get the most up-to-date Postman collection
        let postmanCollection;
        if (latestCollection.content) {
          try {
            postmanCollection = JSON.parse(latestCollection.content);
          } catch (error) {
            console.warn('Failed to parse database content, falling back to in-memory collection');
            postmanCollection = ImportExportService.exportToPostmanCollection(collection);
          }
        } else {
          console.warn('No content in database, using in-memory collection');
          postmanCollection = ImportExportService.exportToPostmanCollection(collection);
        }

        // Ensure proper Postman collection metadata
        if (!postmanCollection.info) {
          postmanCollection.info = {};
        }
        postmanCollection.info.name = latestCollection.name;
        postmanCollection.info.description = latestCollection.description;
        postmanCollection.info.schema = 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json';
        postmanCollection.info._exporter_id = 'backstage-postman-converter';
        postmanCollection.info._collection_link = '';

        const jsonData = JSON.stringify(postmanCollection, null, 2);

        if (exportFormat === 'json') {
          setExportedData(jsonData);
        } else {
          // Download as file
          const blob = new Blob([jsonData], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${collection.name.replace(/\s+/g, '_')}.postman_collection.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          onClose();
        }
      } else {
        // Export environment
        const environment = environments.find(e => e.id === selectedEnvironmentId);
        if (!environment) {
          throw new Error('Please select an environment to export');
        }

        const postmanEnvironment = ImportExportService.exportToPostmanEnvironment(environment);
        const jsonData = JSON.stringify(postmanEnvironment, null, 2);

        if (exportFormat === 'json') {
          setExportedData(jsonData);
        } else {
          // Download as file
          const blob = new Blob([jsonData], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${environment.name.replace(/\s+/g, '_')}.postman_environment.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          onClose();
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Export</DialogTitle>
      <DialogContent>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="Collection" />
          <Tab label="Environment" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Typography variant="body1" gutterBottom>
            Export a collection to Postman format
          </Typography>

          {collections.length === 0 ? (
            <Alert severity="info">No collections available to export</Alert>
          ) : (
            <FormControl fullWidth variant="outlined" margin="normal">
              <InputLabel id="collection-select-label">Collection</InputLabel>
              <Select
                labelId="collection-select-label"
                id="collection-select"
                value={selectedCollectionId}
                onChange={handleCollectionChange}
                label="Collection"
              >
                {collections.map(collection => (
                  <MenuItem key={collection.id} value={collection.id}>
                    {collection.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="body1" gutterBottom>
            Export an environment to Postman format
          </Typography>

          {environments.length === 0 ? (
            <Alert severity="info">No environments available to export</Alert>
          ) : (
            <FormControl fullWidth variant="outlined" margin="normal">
              <InputLabel id="environment-select-label">Environment</InputLabel>
              <Select
                labelId="environment-select-label"
                id="environment-select"
                value={selectedEnvironmentId}
                onChange={handleEnvironmentChange}
                label="Environment"
              >
                {environments.map(environment => (
                  <MenuItem key={environment.id} value={environment.id}>
                    {environment.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        </TabPanel>

        <Box mt={2}>
          <Typography variant="subtitle1" gutterBottom>
            Export Format
          </Typography>
          <RadioGroup
            aria-label="export-format"
            name="export-format"
            value={exportFormat}
            onChange={handleExportFormatChange}
          >
            <FormControlLabel value="json" control={<Radio />} label="Show JSON" />
            <FormControlLabel value="file" control={<Radio />} label="Download as File" />
          </RadioGroup>

          {exportedData && (
            <TextField
              label="Exported JSON"
              multiline
              rows={10}
              variant="outlined"
              fullWidth
              margin="normal"
              value={exportedData}
              InputProps={{
                readOnly: true,
              }}
            />
          )}

          {error && (
            <Box mt={2}>
              <Alert severity="error">{error}</Alert>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button
          onClick={handleExport}
          color="primary"
          variant="contained"
          disabled={
            isLoading ||
            (tabValue === 0 && (!selectedCollectionId || collections.length === 0)) ||
            (tabValue === 1 && (!selectedEnvironmentId || environments.length === 0))
          }
        >
          {isLoading ? <CircularProgress size={24} /> : 'Export'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
