import React from 'react';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import IconButton from '@mui/material/IconButton';
import Collapse from '@mui/material/Collapse';

// Icons
import FolderIcon from '@mui/icons-material/Folder';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import DescriptionIcon from '@mui/icons-material/Description';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import { ApiCollection, ApiFolder } from '../../../types';
import { getMethodColor } from '../utils/requestUtils';

// RenderFolder component for rendering folders and their contents
interface RenderFolderProps {
  folder: ApiFolder;
  collection: ApiCollection;
  level: number;
  expandedFolders: Record<string, boolean>;
  selectedItemId: string | null;
  onFolderToggle: (folderId: string) => void;
  onItemSelect: (itemId: string) => void;
  onContextMenu: (event: React.MouseEvent, itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
}

export const RenderFolder: React.FC<RenderFolderProps> = ({
  folder,
  collection,
  level,
  expandedFolders,
  selectedItemId,
  onFolderToggle,
  onItemSelect,
  onContextMenu,
}) => {
  return (
    <React.Fragment>
      <ListItem
        button
        style={{ paddingLeft: `${level * 16}px` }}
        onClick={() => onFolderToggle(folder.id)}
        onContextMenu={(e) => onContextMenu(e, folder.id, 'folder')}
      >
        <ListItemIcon>
          {expandedFolders[folder.id] ? (
            <FolderOpenIcon style={{ marginLeft: `${level * 8}px` }} />
          ) : (
            <FolderIcon style={{ marginLeft: `${level * 8}px` }} />
          )}
        </ListItemIcon>
        <ListItemText
          primary={folder.name}
          primaryTypographyProps={{
            style: {
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }
          }}
        />
        <IconButton
          edge="end"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onContextMenu(e, folder.id, 'folder');
          }}
        >
          <MoreVertIcon />
        </IconButton>
      </ListItem>

      <Collapse in={expandedFolders[folder.id]} timeout="auto" unmountOnExit>
        {/* Render requests in this folder */}
        {folder.requests.map(requestId => {
          const request = collection.requests[requestId];
          if (!request) return null;

          return (
            <ListItem
              key={requestId}
              button
              style={{ paddingLeft: `${(level + 1) * 16}px` }}
              selected={selectedItemId === requestId}
              onClick={() => onItemSelect(requestId)}
              onContextMenu={(e) => onContextMenu(e, requestId, 'request')}
            >
              <ListItemIcon>
                <DescriptionIcon />
              </ListItemIcon>
              <ListItemText
                primary={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span
                      style={{
                        backgroundColor: getMethodColor(request.method),
                        color: 'white',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        marginRight: '8px',
                        fontWeight: 'bold',
                        minWidth: '60px',
                        textAlign: 'center',
                        fontSize: '12px',
                      }}
                    >
                      {request.method}
                    </span>
                    <span>{request.name}</span>
                  </div>
                }
                secondary={request.url}
                primaryTypographyProps={{
                  style: {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }
                }}
                secondaryTypographyProps={{
                  style: {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }
                }}
              />
              <IconButton
                edge="end"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onContextMenu(e, requestId, 'request');
                }}
              >
                <MoreVertIcon />
              </IconButton>
            </ListItem>
          );
        })}

        {/* Render nested folders */}
        {folder.folders.map(nestedFolder => (
          <RenderFolder
            key={nestedFolder.id}
            folder={nestedFolder}
            collection={collection}
            level={level + 1}
            expandedFolders={expandedFolders}
            selectedItemId={selectedItemId}
            onFolderToggle={onFolderToggle}
            onItemSelect={onItemSelect}
            onContextMenu={onContextMenu}
          />
        ))}
      </Collapse>
    </React.Fragment>
  );
};
