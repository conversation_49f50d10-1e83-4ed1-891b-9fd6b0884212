import React, { useState, useMemo } from 'react';
import Paper from '@mui/material/Paper';
import Card from '@mui/material/Card';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import { styled } from '@mui/material/styles';
import Alert from '@mui/material/Alert';
import { CodeSnippet, EmptyState } from '@backstage/core-components';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';

import { ApiResponse } from '../../../../types';
import { TestResultsPanel } from '../../TestResultsPanel';
import { TabPanel } from '../TabPanel';
import { STATUS_CODE_COLORS } from '../../../../styles';
import {
  getElevatedCardStyles,
  getEnhancedSpacing,
  getEnhancedBorderRadius,
  getEnhancedShadows,
} from '../../../../theme';

// Enhanced styled components
const ResponseCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  height: '100%',
  minHeight: '500px', // Ensure minimum height for response content
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
}));

const ResponseHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.default,
}));

const StatusChip = styled(Chip)<{ statuscode: number }>(({ theme, statuscode }) => {
  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return STATUS_CODE_COLORS.success;
    if (status >= 400) return STATUS_CODE_COLORS.clientError;
    if (status >= 300) return STATUS_CODE_COLORS.redirect;
    return STATUS_CODE_COLORS.info;
  };

  const color = getStatusColor(statuscode);
  return {
    backgroundColor: `${color}15`,
    color: color,
    fontWeight: 600,
    fontSize: '13px',
    '& .MuiChip-icon': {
      color: color,
    },
  };
});

const MetricChip = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  fontWeight: 500,
  fontSize: '12px',
  margin: theme.spacing(0, 0.5),
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.default,
  minHeight: '48px',
  '& .MuiTab-root': {
    textTransform: 'none',
    fontWeight: 500,
    fontSize: '14px',
    minHeight: '48px',
    '&.Mui-selected': {
      fontWeight: 600,
    },
  },
}));

const TabContent = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
  minHeight: '400px', // Ensure minimum height for tab content
  // Remove padding here since TabPanel handles it
}));

const CodeContainer = styled(Paper)(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  minHeight: '300px', // Ensure minimum height for code display
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',

  // Target Backstage CodeSnippet component specifically
  '& .MuiBox-root': {
    height: '100% !important',
    minHeight: '300px !important',
  },
  '& .prism-code': {
    minHeight: '300px !important',
    height: '100% !important',
  },
  '& pre': {
    minHeight: '300px !important',
    height: '100% !important',
    margin: '0 !important',
    borderRadius: '0 !important',
  },
  '& pre[class*="language-"]': {
    minHeight: '300px !important',
    height: '100% !important',
    margin: '0 !important',
    borderRadius: '0 !important',
    fontSize: '14px !important',
    lineHeight: '1.5 !important',
  },
  '& code': {
    fontSize: '14px !important',
    lineHeight: '1.5 !important',
  },
  // Ensure the container takes full height
  '& > div': {
    height: '100% !important',
    minHeight: '300px !important',
  },
}));

const HeadersTable = styled(Table)(({ theme }) => ({
  '& .MuiTableCell-root': {
    padding: theme.spacing(1, 2),
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  '& .MuiTableHead-root .MuiTableCell-root': {
    backgroundColor: theme.palette.background.default,
    fontWeight: 600,
  },
}));

const StyledTableContainer = styled(Paper)(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
}));

// Enhanced TabPanel for better height handling
const EnhancedTabPanel: React.FC<{
  children?: React.ReactNode;
  index: number;
  value: number;
}> = ({ children, value, index, ...other }) => {
  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`response-tabpanel-${index}`}
      aria-labelledby={`response-tab-${index}`}
      sx={{
        height: '100%',
        display: value === index ? 'flex' : 'none',
        flexDirection: 'column',
        overflow: 'hidden',
        minHeight: '400px', // Ensure minimum height for content visibility
      }}
      {...other}
    >
      {value === index && (
        <Box sx={{
          p: 2,
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          minHeight: '350px', // Ensure content area has minimum height
        }}>
          {children}
        </Box>
      )}
    </Box>
  );
};

// Format types for response body display
type ResponseFormat = 'pretty' | 'raw' | 'preview';

interface ResponseViewerProps {
  response: ApiResponse | null;
  responseTabValue: number;
  testResults?: any[];
  isRunningTests?: boolean;
  testError?: string | null;
  onResponseTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
}

export const ResponseViewer: React.FC<ResponseViewerProps> = ({
  response,
  responseTabValue,
  testResults = [],
  isRunningTests = false,
  testError = null,
  onResponseTabChange,
}) => {
  // State for response body format
  const [responseFormat, setResponseFormat] = useState<ResponseFormat>('pretty');

  // Helper function to determine content type
  const getContentType = (headers: Record<string, string>): string => {
    const contentType = headers['content-type'] || headers['Content-Type'] || '';
    return contentType.split(';')[0].toLowerCase();
  };

  // Helper function to detect if content is JSON
  const isJsonContent = (contentType: string, body: string): boolean => {
    if (contentType.includes('application/json') || contentType.includes('text/json')) {
      return true;
    }
    // Try to parse as JSON to detect JSON-like content
    try {
      JSON.parse(body);
      return true;
    } catch {
      return false;
    }
  };

  // Helper function to detect if content is XML
  const isXmlContent = (contentType: string, body: string): boolean => {
    return contentType.includes('xml') || body.trim().startsWith('<');
  };

  // Helper function to detect if content is HTML
  const isHtmlContent = (contentType: string, body: string): boolean => {
    return contentType.includes('text/html') || body.trim().toLowerCase().startsWith('<!doctype html') || body.trim().toLowerCase().startsWith('<html');
  };

  // Format response body based on selected format
  const formatResponseBody = useMemo(() => {
    if (!response?.body) return { text: '', language: 'text' };

    const contentType = getContentType(response.headers);
    const body = response.body;

    switch (responseFormat) {
      case 'pretty':
        if (isJsonContent(contentType, body)) {
          try {
            const parsed = JSON.parse(body);
            return {
              text: JSON.stringify(parsed, null, 2),
              language: 'json'
            };
          } catch {
            return { text: body, language: 'text' };
          }
        } else if (isXmlContent(contentType, body)) {
          // Basic XML formatting (could be enhanced with a proper XML formatter)
          return { text: body, language: 'xml' };
        } else if (isHtmlContent(contentType, body)) {
          return { text: body, language: 'html' };
        }
        return { text: body, language: 'text' };

      case 'raw':
        return { text: body, language: 'text' };

      case 'preview':
        if (isHtmlContent(contentType, body)) {
          // For HTML, we'll show the raw HTML with syntax highlighting
          // In a real implementation, you might want to render it in an iframe
          return { text: body, language: 'html' };
        } else if (isJsonContent(contentType, body)) {
          try {
            const parsed = JSON.parse(body);
            return {
              text: JSON.stringify(parsed, null, 2),
              language: 'json'
            };
          } catch {
            return { text: body, language: 'text' };
          }
        }
        return { text: body, language: 'text' };

      default:
        return { text: body, language: 'text' };
    }
  }, [response?.body, response?.headers, responseFormat]);

  if (!response) {
    return (
      <ResponseCard>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '200px',
          flexDirection: 'column',
          gap: 2
        }}>
          <InfoIcon sx={{ fontSize: 48, color: 'text.disabled' }} />
          <Typography variant="body1" color="text.secondary">
            Send a request to see the response here
          </Typography>
        </Box>
      </ResponseCard>
    );
  }

  const getStatusIcon = (status: number) => {
    if (status >= 200 && status < 300) return <CheckCircleIcon />;
    if (status >= 400) return <ErrorIcon />;
    if (status >= 300) return <WarningIcon />;
    return <InfoIcon />;
  };

  const renderResponseHeader = () => (
    <ResponseHeader>
      <Box display="flex" justifyContent="space-between" alignItems="center" flexWrap="wrap" gap={2}>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
            Response
          </Typography>
          <StatusChip
            statuscode={response.status}
            icon={getStatusIcon(response.status)}
            label={`${response.status} ${response.statusText}`}
            size="small"
          />
        </Box>
        <Box display="flex" alignItems="center" flexWrap="wrap" gap={1}>
          <MetricChip
            label={`${response.time}ms`}
            size="small"
          />
          <MetricChip
            label={`${response.size || 0} bytes`}
            size="small"
          />
          {response.headers['content-type'] && (
            <MetricChip
              label={response.headers['content-type'].split(';')[0]}
              size="small"
            />
          )}
        </Box>
      </Box>
    </ResponseHeader>
  );



  const renderResponseTabs = () => (
    <StyledTabs
      value={responseTabValue}
      onChange={onResponseTabChange}
      indicatorColor="primary"
      textColor="primary"
      variant="scrollable"
      scrollButtons="auto"
    >
      <Tab label="Body" />
      <Tab label="Headers" />
      <Tab label="Cookies" />
      <Tab label="Test Results" />
    </StyledTabs>
  );

  const renderBodyTab = () => {
    const handleFormatChange = (event: any) => {
      setResponseFormat(event.target.value as ResponseFormat);
    };

    return (
      <EnhancedTabPanel value={responseTabValue} index={0}>
        {response.error ? (
          <Alert severity="error" sx={{ borderRadius: 2, mb: 2 }}>
            {response.error}
          </Alert>
        ) : (
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', minHeight: '350px' }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2} sx={{ flexShrink: 0 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Response Body
                {response.body && (
                  <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
                    ({response.body.length} characters)
                  </Typography>
                )}
              </Typography>
              <FormControl variant="outlined" size="small" sx={{ minWidth: '120px' }}>
                <Select
                  value={responseFormat}
                  onChange={handleFormatChange}
                  sx={{ fontSize: '14px' }}
                >
                  <MenuItem value="pretty">Pretty</MenuItem>
                  <MenuItem value="raw">Raw</MenuItem>
                  <MenuItem value="preview">Preview</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <CodeContainer sx={{ flex: 1, minHeight: '300px' }}>
              <Box sx={{
                height: '100%',
                minHeight: '300px',
                '& .prism-code-container': {
                  height: '100% !important',
                  minHeight: '300px !important',
                },
                '& .prism-code': {
                  height: '100% !important',
                  minHeight: '300px !important',
                },
                '& pre[class*="language-"]': {
                  height: '100% !important',
                  minHeight: '300px !important',
                  margin: '0 !important',
                  borderRadius: '0 !important',
                },
              }}>
                <CodeSnippet
                  text={formatResponseBody.text}
                  language={formatResponseBody.language}
                  showCopyCodeButton
                  customStyle={{
                    height: '100%',
                    minHeight: '300px',
                    overflow: 'auto',
                    fontSize: '14px',
                    lineHeight: '1.5',
                  }}
                />
              </Box>
            </CodeContainer>
          </Box>
        )}
      </EnhancedTabPanel>
    );
  };

  const renderHeadersTab = () => (
    <EnhancedTabPanel value={responseTabValue} index={1}>
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'text.primary', mb: 2, flexShrink: 0 }}>
          Response Headers ({Object.keys(response.headers).length})
        </Typography>
        {Object.keys(response.headers).length > 0 ? (
          <StyledTableContainer sx={{ flex: 1, overflow: 'auto' }}>
            <HeadersTable size="small" aria-label="response headers table">
              <TableHead>
                <TableRow>
                  <TableCell>Header</TableCell>
                  <TableCell>Value</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Object.entries(response.headers).map(([key, value]) => (
                  <TableRow key={key} hover>
                    <TableCell component="th" scope="row" sx={{ width: '30%' }}>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 500 }}>
                        {key}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                        {value}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </HeadersTable>
          </StyledTableContainer>
        ) : (
          <EmptyState
            missing="data"
            title="No Headers"
            description="No headers were returned in this response"
          />
        )}
      </Box>
    </EnhancedTabPanel>
  );

  const renderCookiesTab = () => {
    const setCookieHeaders = Object.keys(response.headers).filter(h => h.toLowerCase() === 'set-cookie');
    const hasCookies = setCookieHeaders.length > 0;

    return (
      <EnhancedTabPanel value={responseTabValue} index={2}>
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'text.primary', mb: 2, flexShrink: 0 }}>
            Cookies {hasCookies ? `(${setCookieHeaders.length})` : ''}
          </Typography>
          {hasCookies ? (
            <StyledTableContainer sx={{ flex: 1, overflow: 'auto' }}>
              <HeadersTable size="small" aria-label="cookies table">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Value</TableCell>
                    <TableCell>Domain</TableCell>
                    <TableCell>Path</TableCell>
                    <TableCell>Expires</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* We would parse cookies here in a real implementation */}
                  <TableRow>
                    <TableCell colSpan={5}>
                      <Box sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Cookie parsing not implemented in this demo
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          Raw Set-Cookie headers are available in the Headers tab
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </HeadersTable>
            </StyledTableContainer>
          ) : (
            <EmptyState
              missing="data"
              title="No Cookies"
              description="No cookies were returned in this response"
            />
          )}
        </Box>
      </EnhancedTabPanel>
    );
  };

  const renderTestResultsTab = () => (
    <EnhancedTabPanel value={responseTabValue} index={3}>
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <TestResultsPanel
          results={testResults}
          isRunning={isRunningTests}
          error={testError}
        />
      </Box>
    </EnhancedTabPanel>
  );

  return (
    <ResponseCard>
      {renderResponseHeader()}
      {renderResponseTabs()}
      <TabContent>
        {renderBodyTab()}
        {renderHeadersTab()}
        {renderCookiesTab()}
        {renderTestResultsTab()}
      </TabContent>
    </ResponseCard>
  );
};
