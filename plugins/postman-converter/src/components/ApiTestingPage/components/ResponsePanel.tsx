import React from 'react';
import Typography from '@mui/material/Typography';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Chip from '@mui/material/Chip';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import makeStyles from '@mui/styles/makeStyles';
import { CodeSnippet } from '@backstage/core-components';
import { TabPanel } from './TabPanel';
import { ApiResponse } from '../../../types';
import TimerIcon from '@mui/icons-material/Timer';
import StorageIcon from '@mui/icons-material/Storage'; // For size
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline'; // For success status
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline'; // For error status
import FileCopyIcon from '@mui/icons-material/FileCopy';

const useStyles = makeStyles(theme => ({
  responsePanel: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
  },
  statusChip: {
    marginRight: theme.spacing(1),
  },
  responseTime: {
    display: 'flex',
    alignItems: 'center',
    marginRight: theme.spacing(2),
    color: theme.palette.text.secondary,
  },
  responseSize: {
    display: 'flex',
    alignItems: 'center',
    marginRight: theme.spacing(2),
    color: theme.palette.text.secondary,
  },
  tabContent: {
    padding: theme.spacing(2),
    minHeight: '200px',
  },
  table: {
    minWidth: 650,
  },
  copyButton: {
    marginLeft: 'auto',
  },
  header: { display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: theme.spacing(2) },
}));

interface ResponsePanelProps {
  response: ApiResponse;
  tabValue: number;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
}

export const ResponsePanel: React.FC<ResponsePanelProps> = ({
  response,
  tabValue,
  onTabChange,
}) => {
  const classes = useStyles();

  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };
  // Function to determine status color
  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) {
      return '#4caf50'; // Green for success
    } else if (status >= 300 && status < 400) {
      return '#ff9800'; // Orange for redirection
    } else if (status >= 400 && status < 500) {
      return '#f44336'; // Red for client errors
    } else if (status >= 500) {
      return '#9c27b0'; // Purple for server errors
    }
    return '#757575'; // Grey for unknown
  };

  const getStatusIcon = (status: number) => {
    if (status >= 200 && status < 300) {
      return <CheckCircleOutlineIcon style={{ color: getStatusColor(status), marginRight: '4px' }} />;
    }
    return <ErrorOutlineIcon style={{ color: getStatusColor(status), marginRight: '4px' }} />;
  };

  const copyToClipboard = (text: string) => navigator.clipboard.writeText(text);

  // Function to format response body based on content type
  const formatResponseBody = (body: string, contentType?: string): { formatted: string; language: string } => {
    if (!body) {
      return { formatted: '', language: 'text' };
    }

    if (contentType?.includes('application/json')) {
      try {
        const parsed = JSON.parse(body);
        return {
          formatted: JSON.stringify(parsed, null, 2),
          language: 'json',
        };
      } catch (e) {
        // If parsing fails, return as is
        return { formatted: body, language: 'text' };
      }
    } else if (contentType?.includes('text/html')) {
      return { formatted: body, language: 'html' };
    } else if (contentType?.includes('text/xml') || contentType?.includes('application/xml')) {
      return { formatted: body, language: 'xml' };
    }

    return { formatted: body, language: 'text' };
  };

  const { formatted, language } = formatResponseBody(
    response.body,
    response.headers['content-type']
  );

  return (
    <Paper className={classes.responsePanel}>
      <div className={classes.header}>
        <Box display="flex" alignItems="center">
          <Typography variant="h6" style={{ marginRight: theme.spacing(2) }}>
            Response
          </Typography>
          <Chip
            label={`${response.status} ${response.statusText}`}
            icon={getStatusIcon(response.status)}
            className={classes.statusChip}
            style={{ backgroundColor: getStatusColor(response.status), color: 'white' }}
          />
          <Typography variant="body2" className={classes.responseTime}>
            <TimerIcon fontSize="small" style={{ marginRight: '4px' }} /> {response.time}ms
          </Typography>
          <Typography variant="body2" className={classes.responseSize}>
            <StorageIcon fontSize="small" style={{ marginRight: '4px' }} /> {formatBytes(response.size || 0)}
          </Typography>
        </Box>
        <Button
          size="small"
          startIcon={<FileCopyIcon />}
          onClick={() => copyToClipboard(response.body)}
          className={classes.copyButton}
        >
          Copy Response
        </Button>
      </div>

      <Tabs
        value={tabValue}
        onChange={onTabChange}
        indicatorColor="primary"
        textColor="primary"
        variant="scrollable"
        scrollButtons="auto"
      >
        <Tab label="Body" />
        <Tab label="Headers" />
        <Tab label="Cookies" />
      </Tabs>

      <TabPanel value={tabValue} index={0}>
        <CodeSnippet
          text={formatted}
          language={language}
          showLineNumbers
          showCopyCodeButton
          customStyle={{ minHeight: '200px' }}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Table className={classes.table} size="small">
          <TableHead>
            <TableRow>
              <TableCell>Key</TableCell>
              <TableCell>Value</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Object.entries(response.headers).map(([key, value]) => (
              <TableRow key={key}>
                <TableCell>{key}</TableCell>
                <TableCell>{value}</TableCell>
              </TableRow>
            ))}
            {Object.keys(response.headers).length === 0 && (
              <TableRow>
                <TableCell colSpan={2} align="center">
                  No headers
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="body1">
          Cookie handling will be implemented in a future update.
        </Typography>
      </TabPanel>
    </Paper>
  );
};
