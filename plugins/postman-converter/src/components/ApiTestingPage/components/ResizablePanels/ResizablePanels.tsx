import React, { useState, useCallback, useRef, useEffect } from 'react';
import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import makeStyles from '@mui/styles/makeStyles';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const useStyles = makeStyles(theme => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    minHeight: '600px',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      minHeight: '400px',
    },
  },
  topPanel: {
    overflow: 'auto',
    minHeight: '200px',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      minHeight: '150px',
    },
  },
  bottomPanel: {
    overflow: 'auto',
    minHeight: '150px',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      minHeight: '100px',
    },
  },
  resizer: {
    height: '8px',
    background: theme.palette.divider,
    cursor: 'row-resize',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    transition: 'background-color 0.2s ease',
    outline: 'none',
    '&:hover': {
      backgroundColor: theme.palette.primary.main,
      '& $dragIcon': {
        opacity: 1,
      },
      '& $collapseButton': {
        opacity: 1,
      },
    },
    '&:focus': {
      backgroundColor: theme.palette.primary.main,
      boxShadow: `0 0 0 2px ${theme.palette.primary.light}`,
      '& $dragIcon': {
        opacity: 1,
      },
      '& $collapseButton': {
        opacity: 1,
      },
    },
    '&:active': {
      backgroundColor: theme.palette.primary.dark,
    },
  },
  resizerActive: {
    backgroundColor: theme.palette.primary.main,
  },
  dragIcon: {
    color: theme.palette.text.secondary,
    fontSize: '16px',
    opacity: 0.6,
    transition: 'opacity 0.2s ease',
  },
  resizerLine: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: '1px',
    backgroundColor: theme.palette.divider,
    transform: 'translateY(-50%)',
  },
  collapseButton: {
    position: 'absolute',
    right: theme.spacing(1),
    padding: '2px',
    opacity: 0,
    transition: 'opacity 0.2s ease',
    backgroundColor: theme.palette.background.paper,
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
}));

interface ResizablePanelsProps {
  topPanel: React.ReactNode;
  bottomPanel: React.ReactNode;
  defaultTopHeight?: number; // Percentage (0-100)
  minTopHeight?: number; // Percentage (0-100)
  minBottomHeight?: number; // Percentage (0-100)
  onResize?: (topHeightPercentage: number) => void;
  allowCollapse?: boolean;
}

export const ResizablePanels: React.FC<ResizablePanelsProps> = ({
  topPanel,
  bottomPanel,
  defaultTopHeight = 60,
  minTopHeight = 20,
  minBottomHeight = 20,
  onResize,
  allowCollapse = true,
}) => {
  const classes = useStyles();
  const theme = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [topHeight, setTopHeight] = useState(defaultTopHeight);
  const [isTopCollapsed, setIsTopCollapsed] = useState(false);
  const [isBottomCollapsed, setIsBottomCollapsed] = useState(false);
  const [lastTopHeight, setLastTopHeight] = useState(defaultTopHeight);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!containerRef.current) return;

    const step = 5; // 5% step size for keyboard navigation
    let newHeight = topHeight;

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        newHeight = Math.max(minTopHeight, topHeight - step);
        break;
      case 'ArrowDown':
        e.preventDefault();
        newHeight = Math.min(100 - minBottomHeight, topHeight + step);
        break;
      case 'Home':
        e.preventDefault();
        newHeight = minTopHeight;
        break;
      case 'End':
        e.preventDefault();
        newHeight = 100 - minBottomHeight;
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        // Toggle focus state or provide feedback
        return;
      default:
        return;
    }

    setTopHeight(newHeight);
    onResize?.(newHeight);
  }, [topHeight, minTopHeight, minBottomHeight, onResize]);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing || !containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const containerHeight = containerRect.height;
      const mouseY = e.clientY - containerRect.top;

      // Calculate new top height as percentage
      const newTopHeightPercentage = (mouseY / containerHeight) * 100;

      // Apply constraints
      const constrainedHeight = Math.max(
        minTopHeight,
        Math.min(100 - minBottomHeight, newTopHeightPercentage)
      );

      setTopHeight(constrainedHeight);
      onResize?.(constrainedHeight);
    },
    [isResizing, minTopHeight, minBottomHeight, onResize]
  );

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
  }, []);

  const handleCollapseTop = useCallback(() => {
    if (isTopCollapsed) {
      setTopHeight(lastTopHeight);
      setIsTopCollapsed(false);
    } else {
      setLastTopHeight(topHeight);
      setTopHeight(5); // Minimal height when collapsed
      setIsTopCollapsed(true);
    }
    setIsBottomCollapsed(false);
  }, [isTopCollapsed, topHeight, lastTopHeight]);

  const handleCollapseBottom = useCallback(() => {
    if (isBottomCollapsed) {
      setTopHeight(lastTopHeight);
      setIsBottomCollapsed(false);
    } else {
      setLastTopHeight(topHeight);
      setTopHeight(95); // Maximize top panel when bottom is collapsed
      setIsBottomCollapsed(true);
    }
    setIsTopCollapsed(false);
  }, [isBottomCollapsed, topHeight, lastTopHeight]);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'row-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
    return undefined;
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const bottomHeight = 100 - topHeight;

  return (
    <div ref={containerRef} className={classes.container}>
      {/* Top Panel */}
      <Box
        className={classes.topPanel}
        style={{ height: `${topHeight}%` }}
      >
        {topPanel}
      </Box>

      {/* Resizer */}
      <div
        className={`${classes.resizer} ${isResizing ? classes.resizerActive : ''}`}
        onMouseDown={handleMouseDown}
        onKeyDown={handleKeyDown}
        role="slider"
        aria-orientation="vertical"
        aria-label="Resize panels - use arrow keys to adjust"
        aria-valuenow={Math.round(topHeight)}
        aria-valuemin={Math.round(minTopHeight)}
        aria-valuemax={Math.round(100 - minBottomHeight)}
        tabIndex={0}
      >
        <div className={classes.resizerLine} />
        <DragIndicatorIcon className={classes.dragIcon} />

        {allowCollapse && (
          <>
            <Tooltip title={isTopCollapsed ? "Expand Request Panel" : "Collapse Request Panel"}>
              <IconButton
                size="small"
                className={classes.collapseButton}
                onClick={handleCollapseTop}
                style={{ left: theme.spacing(1) }}
              >
                {isTopCollapsed ? <ExpandMoreIcon fontSize="small" /> : <ExpandLessIcon fontSize="small" />}
              </IconButton>
            </Tooltip>

            <Tooltip title={isBottomCollapsed ? "Expand Response Panel" : "Collapse Response Panel"}>
              <IconButton
                size="small"
                className={classes.collapseButton}
                onClick={handleCollapseBottom}
              >
                {isBottomCollapsed ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
              </IconButton>
            </Tooltip>
          </>
        )}
      </div>

      {/* Bottom Panel */}
      <Box
        className={classes.bottomPanel}
        style={{ height: `${bottomHeight}%` }}
      >
        {bottomPanel}
      </Box>
    </div>
  );
};
