import React from 'react';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import InputLabel from '@mui/material/InputLabel';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { SelectChangeEvent } from '@mui/material/Select';
import makeStyles from '@mui/styles/makeStyles';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import { ApiEnvironment } from '../../../types';

const useStyles = makeStyles(theme => ({
  environmentSelector: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  envSelect: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  envActions: {
    display: 'flex',
  },
}));

interface EnvironmentSelectorProps {
  environments: ApiEnvironment[];
  currentEnvironment: string;
  onEnvironmentChange: (environmentId: string) => void;
  onAddEnvironment: () => void;
  onEditEnvironment: (environmentId: string) => void;
  onImportEnvironment: () => void;
}

export const EnvironmentSelector: React.FC<EnvironmentSelectorProps> = ({
  environments,
  currentEnvironment,
  onEnvironmentChange,
  onAddEnvironment,
  onEditEnvironment,
  onImportEnvironment,
}) => {
  const classes = useStyles();

  const handleEnvironmentChange = (event: SelectChangeEvent<string>) => {
    onEnvironmentChange(event.target.value as string);
  };

  return (
    <Box className={classes.environmentSelector}>
      <FormControl variant="outlined" className={classes.envSelect} size="small">
        <InputLabel id="environment-select-label">Environment</InputLabel>
        <Select
          labelId="environment-select-label"
          id="environment-select"
          value={currentEnvironment}
          onChange={handleEnvironmentChange}
          label="Environment"
        >
          <MenuItem value="">
            <em>No Environment</em>
          </MenuItem>
          {environments.map(env => (
            <MenuItem key={env.id} value={env.id}>
              {env.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <div className={classes.envActions}>
        <Button
          variant="outlined"
          color="primary"
          size="small"
          startIcon={<AddIcon />}
          onClick={onAddEnvironment}
          style={{ marginRight: '8px' }}
        >
          New
        </Button>
        <Button
          variant="outlined"
          color="primary"
          size="small"
          startIcon={<EditIcon />}
          onClick={() => onEditEnvironment(currentEnvironment)}
          disabled={!currentEnvironment}
          style={{ marginRight: '8px' }}
        >
          Edit
        </Button>
        <Button
          variant="outlined"
          color="primary"
          size="small"
          onClick={onImportEnvironment}
        >
          Import
        </Button>
      </div>
    </Box>
  );
};
