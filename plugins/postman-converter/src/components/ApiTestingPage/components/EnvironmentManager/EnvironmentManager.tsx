import React from 'react';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import TextField from '@mui/material/TextField';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Checkbox from '@mui/material/Checkbox';
import { SelectChangeEvent } from '@mui/material/Select';
import { styled } from '@mui/material/styles';
import SettingsIcon from '@mui/icons-material/Settings';
import ImportExportIcon from '@mui/icons-material/ImportExport';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import EnvironmentIcon from '@mui/icons-material/CloudQueue';

import { ApiEnvironment } from '../../../../types';
import {
  getElevatedCardStyles,
  getEnhancedSpacing,
  getEnhancedBorderRadius,
  getEnhancedShadows,
  getButtonStyles,
} from '../../../../theme';

// Enhanced styled components
const EnvironmentCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  marginBottom: theme.spacing(3),
  padding: theme.spacing(3),
}));

const EnvironmentSelector = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  flex: 1,
  '& .MuiOutlinedInput-root': {
    backgroundColor: theme.palette.background.paper,
  },
}));

const ActionButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.spacing(1),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    transform: 'translateY(-1px)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  },
}));

const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: theme.spacing(2),
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
  },
}));

const AddButton = styled(Button)(({ theme }) => ({
  ...getButtonStyles(theme).outlined,
  marginTop: theme.spacing(2),
}));

interface EnvironmentManagerProps {
  environments: ApiEnvironment[];
  currentEnvironment: string;
  selectedEnvironmentId: string;
  environmentDialogOpen: boolean;
  onEnvironmentChange: (event: SelectChangeEvent<string>) => void;
  onOpenEnvironmentDialog: () => void;
  onCloseEnvironmentDialog: () => void;
  onOpenExportDialog: () => void;
  onEnvironmentUpdate: (environments: ApiEnvironment[]) => void;
  onSelectedEnvironmentChange: (environmentId: string) => void;
}

export const EnvironmentManager: React.FC<EnvironmentManagerProps> = ({
  environments,
  currentEnvironment,
  selectedEnvironmentId,
  environmentDialogOpen,
  onEnvironmentChange,
  onOpenEnvironmentDialog,
  onCloseEnvironmentDialog,
  onOpenExportDialog,
  onEnvironmentUpdate,
  onSelectedEnvironmentChange,
}) => {
  const selectedEnvironment = environments.find(env => env.id === selectedEnvironmentId);

  const createNewEnvironment = (): ApiEnvironment => ({
    id: `env_${Date.now()}`,
    name: 'New Environment',
    variables: [],
  });

  const handleAddEnvironment = () => {
    const newEnv = createNewEnvironment();
    onEnvironmentUpdate([...environments, newEnv]);
    onSelectedEnvironmentChange(newEnv.id);
  };

  const handleDeleteEnvironment = (environmentId: string) => {
    const newEnvironments = environments.filter(env => env.id !== environmentId);
    if (newEnvironments.length === 0) {
      newEnvironments.push(createNewEnvironment());
    }
    onEnvironmentUpdate(newEnvironments);
    if (environmentId === selectedEnvironmentId) {
      onSelectedEnvironmentChange(newEnvironments[0].id);
    }
  };

  const handleEnvironmentNameChange = (name: string) => {
    if (!selectedEnvironment) return;

    const newEnvironments = environments.map(env =>
      env.id === selectedEnvironmentId
        ? { ...env, name }
        : env
    );
    onEnvironmentUpdate(newEnvironments);
  };

  const handleVariableChange = (index: number, field: 'key' | 'value' | 'enabled', value: string | boolean) => {
    if (!selectedEnvironment) return;

    const newVariables = [...selectedEnvironment.variables];
    newVariables[index] = {
      ...newVariables[index],
      [field]: value,
    };

    const newEnvironments = environments.map(env =>
      env.id === selectedEnvironmentId
        ? { ...env, variables: newVariables }
        : env
    );
    onEnvironmentUpdate(newEnvironments);
  };

  const handleAddVariable = () => {
    if (!selectedEnvironment) return;

    const newVariables = [...selectedEnvironment.variables, { key: '', value: '', enabled: true }];
    const newEnvironments = environments.map(env =>
      env.id === selectedEnvironmentId
        ? { ...env, variables: newVariables }
        : env
    );
    onEnvironmentUpdate(newEnvironments);
  };

  const handleDeleteVariable = (index: number) => {
    if (!selectedEnvironment) return;

    const newVariables = [...selectedEnvironment.variables];
    newVariables.splice(index, 1);
    const newEnvironments = environments.map(env =>
      env.id === selectedEnvironmentId
        ? { ...env, variables: newVariables }
        : env
    );
    onEnvironmentUpdate(newEnvironments);
  };

  const renderEnvironmentSelector = () => (
    <EnvironmentCard>
      <Box display="flex" alignItems="center" gap={1} mb={2}>
        <EnvironmentIcon color="primary" />
        <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'text.primary' }}>
          Environment
        </Typography>
      </Box>
      <EnvironmentSelector>
        <StyledFormControl size="small">
          <InputLabel id="environment-select-label">Select Environment</InputLabel>
          <Select
            labelId="environment-select-label"
            id="environment-select"
            value={currentEnvironment}
            onChange={onEnvironmentChange}
            label="Select Environment"
          >
            {environments.map(env => (
              <MenuItem key={env.id} value={env.id}>
                {env.name}
              </MenuItem>
            ))}
          </Select>
        </StyledFormControl>
        <Tooltip title="Manage Environments">
          <ActionButton onClick={onOpenEnvironmentDialog} size="small">
            <SettingsIcon />
          </ActionButton>
        </Tooltip>
        <Tooltip title="Import/Export">
          <ActionButton onClick={onOpenExportDialog} size="small">
            <ImportExportIcon />
          </ActionButton>
        </Tooltip>
      </EnvironmentSelector>
    </EnvironmentCard>
  );

  const renderEnvironmentDialog = () => (
    <StyledDialog
      open={environmentDialogOpen}
      onClose={onCloseEnvironmentDialog}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" gap={1}>
            <EnvironmentIcon color="primary" />
            <Typography variant="h5" sx={{ fontWeight: 600 }}>Manage Environments</Typography>
          </Box>
          <IconButton onClick={onCloseEnvironmentDialog} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers sx={{ p: 3 }}>
        <Box mb={3}>
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, color: 'text.primary' }}>
            Environments
          </Typography>
          <Paper variant="outlined" sx={{ borderRadius: 2 }}>
            <List>
              {environments.map((env, index) => (
                <ListItem
                  key={env.id}
                  button
                  selected={env.id === selectedEnvironmentId}
                  onClick={() => onSelectedEnvironmentChange(env.id)}
                  sx={{
                    borderRadius: 1,
                    margin: '4px',
                    '&.Mui-selected': {
                      backgroundColor: 'primary.main',
                      color: 'primary.contrastText',
                      '&:hover': {
                        backgroundColor: 'primary.dark',
                      },
                    },
                  }}
                >
                  <ListItemText
                    primary={env.name}
                    secondary={`${env.variables.length} variables`}
                    secondaryTypographyProps={{
                      sx: { color: env.id === selectedEnvironmentId ? 'primary.contrastText' : 'text.secondary' }
                    }}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleDeleteEnvironment(env.id)}
                      size="small"
                      sx={{ color: env.id === selectedEnvironmentId ? 'primary.contrastText' : 'text.secondary' }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Paper>
          <AddButton
            startIcon={<AddIcon />}
            onClick={handleAddEnvironment}
          >
            Add Environment
          </AddButton>
        </Box>

        {selectedEnvironment && (
          <Box>
            <Box mb={2}>
              <TextField
                label="Environment Name"
                variant="outlined"
                fullWidth
                value={selectedEnvironment.name}
                onChange={(e) => handleEnvironmentNameChange(e.target.value)}
              />
            </Box>

            <Typography variant="subtitle1" gutterBottom>
              Variables
            </Typography>
            <Paper variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Variable</TableCell>
                    <TableCell>Value</TableCell>
                    <TableCell width="100">Enabled</TableCell>
                    <TableCell width="70">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedEnvironment.variables.map((variable, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <TextField
                          size="small"
                          fullWidth
                          value={variable.key}
                          onChange={(e) => handleVariableChange(index, 'key', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          fullWidth
                          value={variable.value}
                          onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Checkbox
                          checked={variable.enabled}
                          onChange={(e) => handleVariableChange(index, 'enabled', e.target.checked)}
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteVariable(index)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Paper>
            <AddButton
              startIcon={<AddIcon />}
              onClick={handleAddVariable}
            >
              Add Variable
            </AddButton>
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onCloseEnvironmentDialog}
          variant="contained"
          color="primary"
        >
          Close
        </Button>
      </DialogActions>
    </StyledDialog>
  );

  return (
    <>
      {renderEnvironmentSelector()}
      {renderEnvironmentDialog()}
    </>
  );
};
