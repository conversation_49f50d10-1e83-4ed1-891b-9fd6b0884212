import React from 'react';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CircularProgress from '@mui/material/CircularProgress';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Badge from '@mui/material/Badge';
import Chip from '@mui/material/Chip';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import Collapse from '@mui/material/Collapse';
import { styled } from '@mui/material/styles';
import { EmptyState, ErrorPanel } from '@backstage/core-components';
import AddIcon from '@mui/icons-material/Add';
import SaveIcon from '@mui/icons-material/Save';
import FolderIcon from '@mui/icons-material/Folder';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CollectionsIcon from '@mui/icons-material/FolderSpecial';

import { ApiCollection } from '../../../../types';
import { RenderFolder } from '../RenderFolder';
import { CollectionContextMenu } from '../ContextMenu/CollectionContextMenu';
import {
  getElevatedCardStyles,
  getEnhancedSpacing,
  getEnhancedBorderRadius,
  getEnhancedShadows,
  getButtonStyles,
} from '../../../../theme';

// Enhanced styled components
const SidebarCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
}));

const SidebarHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.default,
}));

const HeaderTitle = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: theme.spacing(1),
}));

const TitleContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  '& .MuiSvgIcon-root': {
    marginRight: theme.spacing(1),
    color: theme.palette.primary.main,
  },
}));

const SaveAllButton = styled(Button)(({ theme }) => ({
  ...getButtonStyles(theme).primary,
  minWidth: 'auto',
  padding: `${theme.spacing(0.5)} ${theme.spacing(1)}`,
  fontSize: '12px',
  fontWeight: 600,
}));

const CollectionTree = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: theme.spacing(1),
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: theme.palette.background.default,
  },
  '&::-webkit-scrollbar-thumb': {
    background: theme.palette.divider,
    borderRadius: '3px',
    '&:hover': {
      background: theme.palette.text.disabled,
    },
  },
}));

const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '200px',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

interface CollectionsSidebarProps {
  collections: ApiCollection[];
  collectionsLoading: boolean;
  collectionsError: string | null;
  expandedFolders: Record<string, boolean>;
  selectedItemId: string | null;
  unsavedCollections: Set<string>;
  isSaving: Record<string, boolean>;
  onFolderToggle: (folderId: string) => void;
  onItemSelect: (itemId: string) => void;
  onContextMenu: (event: React.MouseEvent, itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
  onAddCollection: () => void;
  onSaveCollection: (collectionId: string) => void;
  onSaveAllCollections: () => void;
}

export const CollectionsSidebar: React.FC<CollectionsSidebarProps> = ({
  collections,
  collectionsLoading,
  collectionsError,
  expandedFolders,
  selectedItemId,
  unsavedCollections,
  isSaving,
  onFolderToggle,
  onItemSelect,
  onContextMenu,
  onAddCollection,
  onSaveCollection,
  onSaveAllCollections,
}) => {
  const renderCollectionHeader = () => (
    <SidebarHeader>
      <HeaderTitle>
        <TitleContent>
          <CollectionsIcon />
          <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
            Collections
          </Typography>
        </TitleContent>
        {unsavedCollections.size > 0 && (
          <SaveAllButton
            size="small"
            startIcon={<SaveIcon />}
            onClick={onSaveAllCollections}
            disabled={Object.values(isSaving).some(saving => saving)}
          >
            Save All ({unsavedCollections.size})
          </SaveAllButton>
        )}
      </HeaderTitle>
      <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '13px' }}>
        Organize your API requests into collections
      </Typography>
    </SidebarHeader>
  );

  const renderLoadingState = () => (
    <LoadingContainer>
      <CircularProgress size={40} />
      <Typography variant="body2" color="text.secondary">
        Loading collections...
      </Typography>
    </LoadingContainer>
  );

  const renderErrorState = () => (
    <Box sx={{ p: 2 }}>
      <ErrorPanel error={collectionsError!} />
    </Box>
  );

  const renderEmptyState = () => (
    <Box sx={{ p: 3 }}>
      <EmptyState
        missing="data"
        title="No collections"
        description="You haven't created any collections yet. Create your first collection to start organizing your API requests."
        action={
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={onAddCollection}
            sx={{ mt: 2 }}
          >
            Create Collection
          </Button>
        }
      />
    </Box>
  );

  const renderCollectionItem = (collection: ApiCollection) => {
    const hasContent = collection.folders.length > 0 || Object.keys(collection.requests).length > 0;
    const isExpanded = expandedFolders[`collection_${collection.id}`] || false;
    const hasUnsavedChanges = unsavedCollections.has(collection.id);
    const isSavingCollection = isSaving[collection.id] || false;

    return (
      <React.Fragment key={collection.id}>
        <ListItem
          button
          selected={selectedItemId === collection.id}
          onClick={() => onItemSelect(collection.id)}
          onContextMenu={(e) => onContextMenu(e, collection.id, 'collection')}
        >
          <ListItemIcon onClick={(e) => {
            e.stopPropagation();
            onFolderToggle(`collection_${collection.id}`);
          }}>
            {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
          </ListItemIcon>
          <ListItemIcon>
            {hasUnsavedChanges ? (
              <Badge color="secondary" variant="dot">
                <FolderIcon color="primary" />
              </Badge>
            ) : (
              <FolderIcon color="primary" />
            )}
          </ListItemIcon>
          <ListItemText
            primary={
              <Box display="flex" alignItems="center">
                <span>{collection.name}</span>
                {hasUnsavedChanges && (
                  <Chip
                    label="Unsaved"
                    size="small"
                    color="secondary"
                    style={{ marginLeft: '8px', fontSize: '11.2px' }}
                  />
                )}
              </Box>
            }
          />
          {hasUnsavedChanges && (
            <Tooltip title="Save collection changes">
              <IconButton
                size="small"
                color="primary"
                disabled={isSavingCollection}
                onClick={(e) => {
                  e.stopPropagation();
                  onSaveCollection(collection.id);
                }}
                style={{ marginRight: '4px' }}
              >
                {isSavingCollection ? (
                  <CircularProgress size={16} />
                ) : (
                  <SaveIcon />
                )}
              </IconButton>
            </Tooltip>
          )}
          <IconButton
            edge="end"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onContextMenu(e, collection.id, 'collection');
            }}
          >
            <MoreVertIcon />
          </IconButton>
        </ListItem>

        {/* Render folders and requests */}
        <Collapse in={isExpanded} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {/* Render orphaned requests at the root level */}
            {collection._orphanedRequests && collection._orphanedRequests.length > 0 && (
              <div>
                <ListItem style={{ paddingLeft: '16px', backgroundColor: '#f5f5f5' }}>
                  <ListItemText
                    primary="Root Requests"
                    secondary={`${collection._orphanedRequests.length} requests not in folders`}
                  />
                </ListItem>
                {collection._orphanedRequests.map(requestId => {
                  const request = collection.requests[requestId];
                  if (!request) return null;

                  return (
                    <RenderFolder
                      key={requestId}
                      folder={null}
                      request={request}
                      collection={collection}
                      level={1}
                      expandedFolders={expandedFolders}
                      selectedItemId={selectedItemId}
                      onFolderToggle={onFolderToggle}
                      onItemSelect={onItemSelect}
                      onContextMenu={onContextMenu}
                    />
                  );
                })}
              </div>
            )}

            {/* Render folders */}
            {collection.folders.map(folder => (
              <RenderFolder
                key={folder.id}
                folder={folder}
                request={null}
                collection={collection}
                level={1}
                expandedFolders={expandedFolders}
                selectedItemId={selectedItemId}
                onFolderToggle={onFolderToggle}
                onItemSelect={onItemSelect}
                onContextMenu={onContextMenu}
              />
            ))}

            {/* Show message if collection is empty */}
            {!hasContent && !collection._orphanedRequests?.length && (
              <ListItem style={{ paddingLeft: '16px' }}>
                <ListItemText
                  primary="Empty collection"
                  secondary="This collection has no requests or folders"
                  primaryTypographyProps={{ style: { color: '#999' } }}
                  secondaryTypographyProps={{ style: { color: '#bbb' } }}
                />
              </ListItem>
            )}
          </List>
        </Collapse>
      </React.Fragment>
    );
  };

  const renderCollectionsList = () => (
    <CollectionTree>
      <List component="nav" aria-label="collections" sx={{ py: 0 }}>
        {collections.map(renderCollectionItem)}
      </List>
    </CollectionTree>
  );

  const renderContent = () => {
    if (collectionsLoading) {
      return renderLoadingState();
    }

    if (collectionsError) {
      return renderErrorState();
    }

    if (collections.length === 0) {
      return renderEmptyState();
    }

    return renderCollectionsList();
  };

  return (
    <SidebarCard>
      {renderCollectionHeader()}
      {renderContent()}
    </SidebarCard>
  );
};
