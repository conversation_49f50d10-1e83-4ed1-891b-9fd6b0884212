import React from 'react';
import Paper from '@mui/material/Paper';
import Card from '@mui/material/Card';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import CircularProgress from '@mui/material/CircularProgress';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import { SelectChangeEvent } from '@mui/material/Select';
import { styled } from '@mui/material/styles';
import SendIcon from '@mui/icons-material/Send';

import { ApiRequest, ApiResponse, ApiEnvironment, HttpMethod } from '../../../../types';
import { TabPanel } from '../TabPanel';
import { ParamsTab } from '../ParamsTab';
import { HeadersTab } from '../HeadersTab';
import { BodyTab } from '../BodyTab';
import { AuthTab } from '../AuthTab';
import { PreRequestScriptPanel } from '../../PreRequestScriptPanel';
import { TestGeneratorPanel } from '../../TestGeneratorPanel';
import { getUrlHelperText } from '../../utils/requestUtils';
import { HTTP_METHOD_COLORS } from '../../../../styles';
import {
  getElevatedCardStyles,
  getEnhancedSpacing,
  getEnhancedBorderRadius,
  getEnhancedShadows,
  getButtonStyles,
} from '../../../../theme';

// Enhanced styled components
const RequestCard = styled(Card)(({ theme }) => ({
  ...getElevatedCardStyles(theme),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
}));

const UrlBar = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'flex-start',
  gap: theme.spacing(2),
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.default,
}));

const MethodSelect = styled(FormControl)(({ theme }) => ({
  minWidth: '120px',
  '& .MuiSelect-select': {
    padding: `${theme.spacing(1)} ${theme.spacing(2)}`,
    fontWeight: 600,
    fontSize: '14px',
  },
}));

const UrlField = styled(TextField)(({ theme }) => ({
  flex: 1,
  '& .MuiOutlinedInput-root': {
    backgroundColor: theme.palette.background.paper,
    '&:hover': {
      backgroundColor: theme.palette.background.paper,
    },
    '&.Mui-focused': {
      backgroundColor: theme.palette.background.paper,
    },
  },
}));

const SendButton = styled(Button)(({ theme }) => ({
  ...getButtonStyles(theme).primary,
  minWidth: '100px',
  height: '56px',
  fontWeight: 600,
  fontSize: '14px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  '&:hover': {
    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.15)',
    transform: 'translateY(-1px)',
  },
  '&:disabled': {
    transform: 'none',
    boxShadow: 'none',
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.default,
  '& .MuiTab-root': {
    textTransform: 'none',
    fontWeight: 500,
    fontSize: '14px',
    minHeight: '48px',
    '&.Mui-selected': {
      fontWeight: 600,
    },
  },
}));

const TabContent = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: theme.spacing(3),
}));

interface RequestBuilderProps {
  currentRequest: ApiRequest;
  currentResponse: ApiResponse | null;
  isLoading: boolean;
  tabValue: number;
  currentEnvironment?: ApiEnvironment;
  onRequestChange: (request: ApiRequest) => void;
  onMethodChange: (event: SelectChangeEvent<HttpMethod>) => void;
  onUrlChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onSendRequest: () => void;
  onSavePreRequestScript: (script: string) => void;
  onSaveTests: (testScript: string) => void;
  onRunTests: (testScript: string) => void;
  onGenerateTests: () => void;
  isSavingPreRequestScript: boolean;
  preRequestScriptError: string | null;
  isGeneratingTests: boolean;
  isRunningTests: boolean;
  testError: string | null;
}

export const RequestBuilder: React.FC<RequestBuilderProps> = ({
  currentRequest,
  currentResponse,
  isLoading,
  tabValue,
  currentEnvironment,
  onRequestChange,
  onMethodChange,
  onUrlChange,
  onTabChange,
  onSendRequest,
  onSavePreRequestScript,
  onSaveTests,
  onRunTests,
  onGenerateTests,
  isSavingPreRequestScript,
  preRequestScriptError,
  isGeneratingTests,
  isRunningTests,
  testError,
}) => {
  const getMethodColor = (method: HttpMethod) => {
    return HTTP_METHOD_COLORS[method] || HTTP_METHOD_COLORS.GET;
  };

  const renderUrlBar = () => (
    <UrlBar>
      <MethodSelect>
        <Select
          value={currentRequest.method}
          onChange={onMethodChange}
          variant="outlined"
          size="small"
          sx={{
            '& .MuiSelect-select': {
              color: getMethodColor(currentRequest.method),
              fontWeight: 600,
            },
          }}
        >
          {Object.entries(HTTP_METHOD_COLORS).map(([method, color]) => (
            <MenuItem key={method} value={method} sx={{ color, fontWeight: 600 }}>
              {method}
            </MenuItem>
          ))}
        </Select>
      </MethodSelect>
      <UrlField
        variant="outlined"
        size="small"
        placeholder="Enter request URL (e.g., https://api.example.com/users)"
        value={currentRequest.url}
        onChange={onUrlChange}
        helperText={getUrlHelperText(currentRequest.url)}
        FormHelperTextProps={{
          sx: { fontSize: '12px', mt: 0.5 }
        }}
      />
      <SendButton
        variant="contained"
        startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
        onClick={onSendRequest}
        disabled={isLoading || !currentRequest.url}
      >
        {isLoading ? 'Sending...' : 'Send'}
      </SendButton>
    </UrlBar>
  );

  const renderTabs = () => (
    <StyledTabs
      value={tabValue}
      onChange={onTabChange}
      indicatorColor="primary"
      textColor="primary"
      variant="scrollable"
      scrollButtons="auto"
    >
      <Tab label="Params" />
      <Tab label="Headers" />
      <Tab label="Body" />
      <Tab label="Auth" />
      <Tab label="Pre-request" />
      <Tab label="Tests" />
    </StyledTabs>
  );

  const renderTabPanels = () => (
    <TabContent>
      {/* Params tab */}
      <TabPanel value={tabValue} index={0}>
        <ParamsTab
          params={currentRequest.params}
          onParamsChange={(params) => onRequestChange({ ...currentRequest, params })}
        />
      </TabPanel>

      {/* Headers tab */}
      <TabPanel value={tabValue} index={1}>
        <HeadersTab
          headers={currentRequest.headers}
          onHeadersChange={(headers) => onRequestChange({ ...currentRequest, headers })}
        />
      </TabPanel>

      {/* Body tab */}
      <TabPanel value={tabValue} index={2}>
        <BodyTab
          body={currentRequest.body}
          onBodyChange={(body) => onRequestChange({ ...currentRequest, body })}
        />
      </TabPanel>

      {/* Auth tab */}
      <TabPanel value={tabValue} index={3}>
        <AuthTab
          auth={currentRequest.auth}
          onAuthChange={(auth) => onRequestChange({ ...currentRequest, auth })}
        />
      </TabPanel>

      {/* Pre-request script tab */}
      <TabPanel value={tabValue} index={4}>
        <PreRequestScriptPanel
          request={currentRequest}
          onSaveScript={onSavePreRequestScript}
          isSaving={isSavingPreRequestScript}
          error={preRequestScriptError}
        />
      </TabPanel>

      {/* Tests tab */}
      <TabPanel value={tabValue} index={5}>
        <TestGeneratorPanel
          request={currentRequest}
          response={currentResponse}
          onRunTests={onRunTests}
          onSaveTests={onSaveTests}
          onGenerateTests={onGenerateTests}
          isGenerating={isGeneratingTests}
          isRunning={isRunningTests}
          error={testError}
        />
      </TabPanel>
    </TabContent>
  );

  return (
    <RequestCard>
      {renderUrlBar()}
      {renderTabs()}
      {renderTabPanels()}
    </RequestCard>
  );
};
