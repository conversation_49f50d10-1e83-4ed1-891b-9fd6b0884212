import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Paper from '@mui/material/Paper';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import IconButton from '@mui/material/IconButton';
import Table from '@mui/material/Table';
import TableHead from '@mui/material/TableHead';
import TableBody from '@mui/material/TableBody';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import Checkbox from '@mui/material/Checkbox';
import makeStyles from '@mui/styles/makeStyles';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';
import { ApiEnvironment } from '../../types';
import { createNewEnvironment } from './utils/requestUtils';

const useStyles = makeStyles(theme => ({
  addButton: {
    marginTop: theme.spacing(1),
  },
}));

interface EnvironmentDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (environment: ApiEnvironment) => void;
  environment: ApiEnvironment | null;
}

export const EnvironmentDialog: React.FC<EnvironmentDialogProps> = ({
  open,
  onClose,
  onSave,
  environment,
}) => {
  const classes = useStyles();
  const [currentEnvironment, setCurrentEnvironment] = useState<ApiEnvironment | null>(null);

  useEffect(() => {
    if (environment) {
      setCurrentEnvironment({ ...environment });
    } else if (open) {
      // Create a new environment if none is provided
      setCurrentEnvironment(createNewEnvironment());
    }
  }, [environment, open]);

  const handleSave = () => {
    if (currentEnvironment) {
      onSave(currentEnvironment);
      onClose();
    }
  };

  const handleClose = () => {
    setCurrentEnvironment(null);
    onClose();
  };

  const handleNameChange = (name: string) => {
    if (currentEnvironment) {
      setCurrentEnvironment({
        ...currentEnvironment,
        name,
      });
    }
  };

  const handleVariableChange = (index: number, field: 'key' | 'value' | 'enabled', value: string | boolean) => {
    if (currentEnvironment) {
      const newVariables = [...currentEnvironment.variables];
      newVariables[index] = {
        ...newVariables[index],
        [field]: value,
      };
      setCurrentEnvironment({
        ...currentEnvironment,
        variables: newVariables,
      });
    }
  };

  const handleAddVariable = () => {
    if (currentEnvironment) {
      setCurrentEnvironment({
        ...currentEnvironment,
        variables: [
          ...currentEnvironment.variables,
          { key: '', value: '', enabled: true },
        ],
      });
    }
  };

  const handleDeleteVariable = (index: number) => {
    if (currentEnvironment) {
      const newVariables = [...currentEnvironment.variables];
      newVariables.splice(index, 1);
      setCurrentEnvironment({
        ...currentEnvironment,
        variables: newVariables,
      });
    }
  };

  if (!currentEnvironment) {
    return null;
  }

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md">
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {environment ? 'Edit Environment' : 'New Environment'}
          </Typography>
          <IconButton onClick={handleClose} size="large">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Box mb={2}>
          <TextField
            label="Environment Name"
            variant="outlined"
            fullWidth
            value={currentEnvironment.name}
            onChange={(e) => handleNameChange(e.target.value)}
          />
        </Box>

        <Typography variant="subtitle1" gutterBottom>
          Variables
        </Typography>
        <Paper variant="outlined">
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Variable</TableCell>
                <TableCell>Value</TableCell>
                <TableCell width="100">Enabled</TableCell>
                <TableCell width="70">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentEnvironment.variables.map((variable, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <TextField
                      size="small"
                      fullWidth
                      value={variable.key}
                      onChange={(e) => handleVariableChange(index, 'key', e.target.value)}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      size="small"
                      fullWidth
                      value={variable.value}
                      onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Checkbox
                      checked={variable.enabled}
                      onChange={(e) => handleVariableChange(index, 'enabled', e.target.checked)}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteVariable(index)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Paper>
        <Button
          startIcon={<AddIcon />}
          color="primary"
          className={classes.addButton}
          onClick={handleAddVariable}
        >
          Add Variable
        </Button>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="primary">
          Cancel
        </Button>
        <Button onClick={handleSave} color="primary" variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};
