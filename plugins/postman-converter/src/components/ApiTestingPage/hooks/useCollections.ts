import { useState, useCallback } from 'react';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { useAsync } from 'react-use';
import { ApiCollection, Collection, ApiFolder, ApiRequest, HttpMethod } from '../../../types';
import { postmanConverterApiRef } from '../../../api';
import { convertToApiCollection } from '../utils/collectionUtils';
import { useTypedTaskManager } from './useTaskManager';

export const useCollections = () => {
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);
  const taskManager = useTypedTaskManager();

  const [collections, setCollections] = useState<ApiCollection[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [unsavedCollections, setUnsavedCollections] = useState<Set<string>>(new Set());
  const [isSaving, setIsSaving] = useState<Record<string, boolean>>({});

  // Helper function to mark a collection as having unsaved changes
  const markCollectionAsUnsaved = useCallback((collectionId: string) => {
    setUnsavedCollections(prev => new Set([...prev, collectionId]));
  }, []);

  // Helper function to mark a collection as saved
  const markCollectionAsSaved = useCallback((collectionId: string) => {
    setUnsavedCollections(prev => {
      const newSet = new Set(prev);
      newSet.delete(collectionId);
      return newSet;
    });
  }, []);

  // Fetch collections from the API
  const { loading: collectionsLoading, error: collectionsError } = useAsync(async () => {
    try {
      const fetchedCollections = await postmanConverterApi.getCollections();

      // Convert backend collections to frontend ApiCollection format
      const apiCollections: ApiCollection[] = [];

      for (const collection of fetchedCollections) {
        const apiCollection = await convertToApiCollection(collection, errorApi, postmanConverterApi);
        apiCollections.push(apiCollection);
      }

      // Update collections state
      if (apiCollections.length > 0) {
        setCollections(apiCollections);
      }

      return fetchedCollections;
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      errorApi.post(error);
      throw error;
    }
  }, []);

  // Handle folder toggle
  const handleFolderToggle = useCallback((folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId],
    }));
  }, []);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string) => {
    setSelectedItemId(itemId);
  }, []);

  // Handle add collection with TaskMaster
  const handleAddCollection = useCallback(async () => {
    return taskManager.executeCollectionTask('create', async () => {
      // Create a basic empty Postman collection structure
      const emptyCollection = {
        info: {
          name: 'New Collection',
          description: 'A new collection',
          schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
        },
        item: []
      };

      // Create a new collection in the database
      const newCollection = await postmanConverterApi.createCollection({
        name: 'New Collection',
        description: 'A new collection',
        content: JSON.stringify(emptyCollection)
      });

      // Convert to ApiCollection format
      const apiCollection = await convertToApiCollection(newCollection, errorApi, postmanConverterApi);

      // Add to collections state
      setCollections(prevCollections => [...prevCollections, apiCollection]);

      return { success: true, collection: apiCollection };
    });
  }, [postmanConverterApi, errorApi, taskManager]);

  // Handle delete collection
  const handleDeleteCollection = useCallback(async (collectionId: string) => {
    try {
      await postmanConverterApi.deleteCollection(collectionId);

      // Remove from collections state
      setCollections(prevCollections =>
        prevCollections.filter(c => c.id !== collectionId)
      );

      // Clear selected item if it was in this collection
      if (selectedItemId) {
        const collection = collections.find(c => c.id === collectionId);
        if (collection && collection.requests[selectedItemId]) {
          setSelectedItemId(null);
        }
      }

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi, collections, selectedItemId]);

  // Handle import collection
  const handleImportCollection = useCallback(async (apiCollection: ApiCollection) => {
    // Convert ApiCollection to Postman collection format
    const postmanCollection = {
      info: {
        name: apiCollection.name,
        description: apiCollection.description,
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: [] // We would need to convert the folders and requests structure here
    };

    try {
      // Save to database
      const newCollection = await postmanConverterApi.createCollection({
        name: apiCollection.name,
        description: apiCollection.description,
        content: JSON.stringify(postmanCollection)
      });

      // Convert back to ApiCollection format
      const savedApiCollection = await convertToApiCollection(newCollection, errorApi, postmanConverterApi);

      // Add to collections state
      setCollections(prevCollections => [...prevCollections, savedApiCollection]);

      return { success: true, collection: savedApiCollection };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi]);

  // Helper function to create a new folder
  const createNewFolder = (name: string, parentId?: string): ApiFolder => ({
    id: `folder_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    name,
    parentId,
    requests: [],
    folders: [],
    description: '',
  });

  // Helper function to create a new request
  const createNewRequest = (name: string, method: HttpMethod = 'GET', url: string = ''): ApiRequest => ({
    id: `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    name,
    method,
    url,
    headers: [],
    params: [],
    body: {
      mode: 'none',
      enabled: true,
    },
    preRequestScript: '',
    testScript: '',
    testScriptMetadata: {
      isAutoGenerated: false,
      hasUnsavedAutoGenerated: false,
    },
  });

  // Handle add folder
  const handleAddFolder = useCallback(async (collectionId: string, parentFolderId?: string, folderName: string = 'New Folder') => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      const newFolder = createNewFolder(folderName, parentFolderId);

      // Update the collection structure
      const updatedCollection = { ...collection };

      if (parentFolderId) {
        // Add to a specific folder
        const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
          return folders.map(folder => {
            if (folder.id === parentFolderId) {
              return {
                ...folder,
                folders: [...folder.folders, newFolder],
              };
            }
            if (folder.folders.length > 0) {
              return {
                ...folder,
                folders: updateFolders(folder.folders),
              };
            }
            return folder;
          });
        };

        updatedCollection.folders = updateFolders(updatedCollection.folders);
      } else {
        // Add to root level
        updatedCollection.folders = [...updatedCollection.folders, newFolder];
      }

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Mark collection as having unsaved changes
      markCollectionAsUnsaved(collectionId);

      // TODO: Update the collection in the database
      // This would require converting the ApiCollection back to Postman format
      // and calling postmanConverterApi.updateCollection

      return { success: true, folder: newFolder };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [collections, errorApi, markCollectionAsUnsaved]);

  // Handle add request
  const handleAddRequest = useCallback(async (
    collectionId: string,
    parentFolderId?: string,
    requestName: string = 'New Request',
    method: HttpMethod = 'GET',
    url: string = ''
  ) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      const newRequest = createNewRequest(requestName, method, url);

      // Update the collection structure
      const updatedCollection = { ...collection };

      // Add the request to the requests object
      updatedCollection.requests = {
        ...updatedCollection.requests,
        [newRequest.id]: newRequest,
      };

      if (parentFolderId) {
        // Add to a specific folder
        const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
          return folders.map(folder => {
            if (folder.id === parentFolderId) {
              return {
                ...folder,
                requests: [...folder.requests, newRequest.id],
              };
            }
            if (folder.folders.length > 0) {
              return {
                ...folder,
                folders: updateFolders(folder.folders),
              };
            }
            return folder;
          });
        };

        updatedCollection.folders = updateFolders(updatedCollection.folders);
      } else {
        // Add to orphaned requests (root level)
        updatedCollection._orphanedRequests = [
          ...(updatedCollection._orphanedRequests || []),
          newRequest.id,
        ];
      }

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Mark collection as having unsaved changes
      markCollectionAsUnsaved(collectionId);

      // TODO: Update the collection in the database
      // This would require converting the ApiCollection back to Postman format
      // and calling postmanConverterApi.updateCollection

      return { success: true, request: newRequest };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [collections, errorApi, markCollectionAsUnsaved]);

  // Handle rename collection
  const handleRenameCollection = useCallback(async (collectionId: string, newName: string) => {
    try {
      // Update in database
      await postmanConverterApi.updateCollection(collectionId, { name: newName });

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? { ...c, name: newName } : c
        )
      );

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi]);

  // Handle rename folder
  const handleRenameFolder = useCallback(async (collectionId: string, folderId: string, newName: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      // Update the folder name in the collection structure
      const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
        return folders.map(folder => {
          if (folder.id === folderId) {
            return { ...folder, name: newName };
          }
          if (folder.folders.length > 0) {
            return {
              ...folder,
              folders: updateFolders(folder.folders),
            };
          }
          return folder;
        });
      };

      const updatedCollection = {
        ...collection,
        folders: updateFolders(collection.folders),
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Mark collection as having unsaved changes
      markCollectionAsUnsaved(collectionId);

      // TODO: Update the collection in the database

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [collections, errorApi, markCollectionAsUnsaved]);

  // Handle rename request
  const handleRenameRequest = useCallback(async (collectionId: string, requestId: string, newName: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection || !collection.requests[requestId]) {
        throw new Error('Collection or request not found');
      }

      const updatedCollection = {
        ...collection,
        requests: {
          ...collection.requests,
          [requestId]: {
            ...collection.requests[requestId],
            name: newName,
          },
        },
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Mark collection as having unsaved changes
      markCollectionAsUnsaved(collectionId);

      // TODO: Update the collection in the database

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [collections, errorApi, markCollectionAsUnsaved]);

  // Helper function to auto-save unsaved auto-generated test scripts
  const autoSaveUnsavedTestScripts = useCallback((collection: ApiCollection): ApiCollection => {
    const updatedRequests = { ...collection.requests };
    let hasChanges = false;

    // Check each request for unsaved auto-generated test scripts
    Object.keys(updatedRequests).forEach(requestId => {
      const request = updatedRequests[requestId];
      if (request.testScriptMetadata?.hasUnsavedAutoGenerated && request.testScript) {
        // Auto-save the test script by clearing the unsaved flag
        updatedRequests[requestId] = {
          ...request,
          testScriptMetadata: {
            ...request.testScriptMetadata,
            hasUnsavedAutoGenerated: false,
          }
        };
        hasChanges = true;
      }
    });

    if (hasChanges) {
      return {
        ...collection,
        requests: updatedRequests
      };
    }

    return collection;
  }, []);

  // Helper function to check if a collection has unsaved auto-generated test scripts
  const hasUnsavedAutoGeneratedTests = useCallback((collectionId: string): boolean => {
    const collection = collections.find(c => c.id === collectionId);
    if (!collection) return false;

    return Object.values(collection.requests).some(request =>
      request.testScriptMetadata?.hasUnsavedAutoGenerated === true
    );
  }, [collections]);

  // Helper function to get count of unsaved auto-generated test scripts in a collection
  const getUnsavedAutoGeneratedTestsCount = useCallback((collectionId: string): number => {
    const collection = collections.find(c => c.id === collectionId);
    if (!collection) return 0;

    return Object.values(collection.requests).filter(request =>
      request.testScriptMetadata?.hasUnsavedAutoGenerated === true
    ).length;
  }, [collections]);

  // Handle save collection with TaskMaster
  const handleSaveCollection = useCallback(async (collectionId: string) => {
    const collection = collections.find(c => c.id === collectionId);
    if (!collection) {
      throw new Error('Collection not found');
    }

    setIsSaving(prev => ({ ...prev, [collectionId]: true }));

    try {
      return await taskManager.executeCollectionTask(`save_${collectionId}`, async () => {
        // Auto-save any unsaved auto-generated test scripts before saving collection
        const collectionWithAutoSavedTests = autoSaveUnsavedTestScripts(collection);

        // Update the collection in state if auto-save made changes
        if (collectionWithAutoSavedTests !== collection) {
          setCollections(prevCollections =>
            prevCollections.map(c =>
              c.id === collectionId ? collectionWithAutoSavedTests : c
            )
          );
        }

        // Convert ApiCollection back to Postman format
        const postmanCollection = convertApiCollectionToPostman(collectionWithAutoSavedTests);

        // Update the collection in the database
        await postmanConverterApi.updateCollection(collectionId, {
          content: JSON.stringify(postmanCollection)
        });

        // Mark as saved
        markCollectionAsSaved(collectionId);

        return { success: true, collection: collectionWithAutoSavedTests };
      });
    } finally {
      setIsSaving(prev => ({ ...prev, [collectionId]: false }));
    }
  }, [collections, postmanConverterApi, taskManager, markCollectionAsSaved, autoSaveUnsavedTestScripts, setCollections]);

  // Helper function to convert ApiCollection back to Postman collection format
  const convertApiCollectionToPostman = useCallback((apiCollection: ApiCollection): any => {
    const postmanCollection = {
      info: {
        name: apiCollection.name,
        description: apiCollection.description,
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: [] as any[]
    };

    // Helper function to convert ApiRequest to Postman request
    const convertRequestToPostman = (request: ApiRequest): any => {
      // Build proper Postman URL object
      const buildPostmanUrl = (url: string, params: { key: string; value: string; enabled: boolean }[]): any => {
        const urlResult: any = { raw: url || '' };

        if (!url) {
          return {
            raw: '',
            query: params.filter(p => p.key).map(p => ({
              key: p.key,
              value: p.value,
              disabled: !p.enabled
            }))
          };
        }

        // Handle URLs with variables like {{base_url}} first (before URL parsing)
        if (url.includes('{{') && url.includes('}}')) {
          const hostMatch = url.match(/\{\{([^}]+)\}\}/);
          if (hostMatch) {
            urlResult.host = [hostMatch[0]];
          }

          // Extract path from environment variable URLs manually
          // Look for path after the variable: {{base_url}}/path/to/resource
          const pathAfterVariable = url.replace(/\{\{[^}]+\}\}/, '');
          if (pathAfterVariable && pathAfterVariable.startsWith('/') && pathAfterVariable !== '/') {
            const pathSegments = pathAfterVariable.substring(1).split('/').filter(Boolean);
            urlResult.path = pathSegments;

            // Handle path variables like :globalId
            const pathVariables: any[] = [];
            pathSegments.forEach(segment => {
              if (segment.startsWith(':')) {
                pathVariables.push({
                  key: segment.substring(1),
                  value: ''
                });
              }
            });
            if (pathVariables.length > 0) {
              urlResult.variable = pathVariables;
            }
          }
        } else {
          try {
            // Parse regular URLs
            const urlObj = new URL(url);
            urlResult.host = urlObj.hostname ? urlObj.hostname.split('.') : [];

            // Extract path components using URL pathname to avoid hostname duplication
            const pathname = urlObj.pathname;
            if (pathname && pathname !== '/') {
              // Remove leading slash and split into segments
              const pathSegments = pathname.substring(1).split('/').filter(Boolean);
              urlResult.path = pathSegments;

              // Handle path variables like :globalId
              const pathVariables: any[] = [];
              pathSegments.forEach(segment => {
                if (segment.startsWith(':')) {
                  pathVariables.push({
                    key: segment.substring(1),
                    value: ''
                  });
                }
              });
              if (pathVariables.length > 0) {
                urlResult.variable = pathVariables;
              }
            }
          } catch (urlError) {
            console.warn(`Failed to parse regular URL: ${url}`, urlError);
          }
        }

        try {

          // Handle query parameters
          const allParams: any[] = [];

          // Extract existing query parameters from URL
          if (url.includes('?')) {
            const queryString = url.split('?')[1];
            if (queryString) {
              queryString.split('&').forEach(param => {
                const [key, value] = param.split('=');
                if (key) {
                  allParams.push({
                    key: decodeURIComponent(key),
                    value: value ? decodeURIComponent(value) : '',
                    disabled: false
                  });
                }
              });
            }
          }

          // Add additional params
          params.filter(p => p.key && !allParams.some(ep => ep.key === p.key))
            .forEach(p => {
              allParams.push({
                key: p.key,
                value: p.value,
                disabled: !p.enabled
              });
            });

          if (allParams.length > 0) {
            urlResult.query = allParams;
          }

        } catch (error) {
          console.warn(`Failed to parse URL: ${url}`, error);
          if (params.length > 0) {
            urlResult.query = params.filter(p => p.key).map(p => ({
              key: p.key,
              value: p.value,
              disabled: !p.enabled
            }));
          }
        }

        return urlResult;
      };

      const postmanRequest: any = {
        name: request.name,
        request: {
          method: request.method,
          header: request.headers
            .filter(h => h.key) // Only include headers with keys
            .map(h => ({
              key: h.key,
              value: h.value,
              type: 'default',
              disabled: !h.enabled
            })),
          url: buildPostmanUrl(request.url, request.params)
        }
      };

      // Add authentication if present
      if (request.auth && request.auth.type !== 'none') {
        if (request.auth.type === 'bearer' && request.auth.bearer?.token) {
          postmanRequest.request.auth = {
            type: 'bearer',
            bearer: [
              {
                key: 'token',
                value: request.auth.bearer.token,
                type: 'string'
              }
            ]
          };
        } else if (request.auth.type === 'basic' && request.auth.basic) {
          postmanRequest.request.auth = {
            type: 'basic',
            basic: [
              {
                key: 'username',
                value: request.auth.basic.username || '',
                type: 'string'
              },
              {
                key: 'password',
                value: request.auth.basic.password || '',
                type: 'string'
              }
            ]
          };
        } else if (request.auth.type === 'apiKey' && request.auth.apiKey) {
          postmanRequest.request.auth = {
            type: 'apikey',
            apikey: [
              {
                key: 'key',
                value: request.auth.apiKey.key || '',
                type: 'string'
              },
              {
                key: 'value',
                value: request.auth.apiKey.value || '',
                type: 'string'
              },
              {
                key: 'in',
                value: request.auth.apiKey.in || 'header',
                type: 'string'
              }
            ]
          };
        }
      }

      // Add body if present
      if (request.body.mode !== 'none') {
        postmanRequest.request.body = {
          mode: request.body.mode
        };

        if (request.body.mode === 'raw' && request.body.raw) {
          postmanRequest.request.body.raw = request.body.raw;
          // Add options for raw body to specify language
          postmanRequest.request.body.options = {
            raw: {
              language: 'json'
            }
          };
        } else if (request.body.mode === 'form-data' && request.body.formData) {
          postmanRequest.request.body.formdata = request.body.formData.map(fd => ({
            key: fd.key,
            value: fd.value,
            type: fd.type
          }));
        } else if (request.body.mode === 'urlencoded' && request.body.urlencoded) {
          postmanRequest.request.body.urlencoded = request.body.urlencoded.map(ue => ({
            key: ue.key,
            value: ue.value
          }));
        }
      }

      // Add events (scripts)
      const events = [];
      if (request.preRequestScript) {
        events.push({
          listen: 'prerequest',
          script: {
            exec: request.preRequestScript.split('\n'),
            type: 'text/javascript'
          }
        });
      }
      if (request.testScript) {

        events.push({
          listen: 'test',
          script: {
            exec: request.testScript.split('\n'),
            type: 'text/javascript'
          }
        });
      }
      if (events.length > 0) {
        postmanRequest.event = events;
      }

      return postmanRequest;
    };

    // Helper function to convert ApiFolder to Postman folder
    const convertFolderToPostman = (folder: ApiFolder): any => {
      const postmanFolder = {
        name: folder.name,
        item: [] as any[]
      };

      // Add requests in this folder
      folder.requests.forEach(requestId => {
        const request = apiCollection.requests[requestId];
        if (request) {
          postmanFolder.item.push(convertRequestToPostman(request));
        }
      });

      // Add nested folders
      folder.folders.forEach(nestedFolder => {
        postmanFolder.item.push(convertFolderToPostman(nestedFolder));
      });

      return postmanFolder;
    };

    // Add root-level folders
    apiCollection.folders.forEach(folder => {
      postmanCollection.item.push(convertFolderToPostman(folder));
    });

    // Add orphaned requests (requests not in any folder)
    if (apiCollection._orphanedRequests) {
      apiCollection._orphanedRequests.forEach(requestId => {
        const request = apiCollection.requests[requestId];
        if (request) {
          postmanCollection.item.push(convertRequestToPostman(request));
        }
      });
    }

    return postmanCollection;
  }, []);

  return {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    setSelectedItemId,
    unsavedCollections,
    isSaving,
    taskManager,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleDeleteCollection,
    handleImportCollection,
    handleAddFolder,
    handleAddRequest,
    handleRenameCollection,
    handleRenameFolder,
    handleRenameRequest,
    handleSaveCollection,
    markCollectionAsUnsaved,
    markCollectionAsSaved,
    hasUnsavedAutoGeneratedTests,
    getUnsavedAutoGeneratedTestsCount,
  };
};
