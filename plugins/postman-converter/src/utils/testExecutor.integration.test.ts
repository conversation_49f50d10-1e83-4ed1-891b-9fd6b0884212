import { executeTests } from './testExecutor';

describe('testExecutor Integration Tests', () => {
  // Test the exact scenario the user is experiencing with detailed logging
  it('should demonstrate the exact failing scenario step by step', () => {
    // Simulate a real API response with status 200
    const realApiResponse = {
      status: 200,
      statusText: 'OK',
      headers: { 
        'content-type': 'application/json',
        'server': 'nginx/1.18.0'
      },
      body: '{"id": 1, "name": "Test User", "email": "<EMAIL>"}',
      time: 245,
      size: 58
    };

    // Test script that should FAIL because it expects status 20999 but gets 200
    const failingTestScript = `
pm.test("Status code should be 20999 (this should fail)", function () {
    pm.response.to.have.status(20999);
});

pm.test("Response should be JSON", function () {
    pm.response.to.be.json;
});
`;

    console.log('\n=== INTEGRATION TEST: Failing Status Code Assertion ===');
    console.log('API Response Status:', realApiResponse.status);
    console.log('Test Script Expects:', 20999);
    console.log('Expected Result: FAIL');
    console.log('');

    // Execute the test
    const results = executeTests(realApiResponse, failingTestScript);

    console.log('Test Results:');
    results.forEach((result, index) => {
      console.log(`  Test ${index + 1}:`);
      console.log(`    Name: ${result.name}`);
      console.log(`    Passed: ${result.passed}`);
      console.log(`    Error: ${result.error || 'None'}`);
      console.log(`    Duration: ${result.duration}ms`);
      console.log('');
    });

    // Verify the first test (status code) fails
    expect(results).toHaveLength(2);
    expect(results[0].name).toBe("Status code should be 20999 (this should fail)");
    expect(results[0].passed).toBe(false);
    expect(results[0].error).toContain('expected 200 to equal 20999');

    // Verify the second test (JSON) passes
    expect(results[1].name).toBe("Response should be JSON");
    expect(results[1].passed).toBe(true);
  });

  it('should test various incorrect status codes to ensure they all fail', () => {
    const response200 = {
      status: 200,
      statusText: 'OK',
      headers: { 'content-type': 'application/json' },
      body: '{}',
      time: 100,
      size: 2
    };

    const testCases = [
      { expected: 201, shouldPass: false },
      { expected: 404, shouldPass: false },
      { expected: 500, shouldPass: false },
      { expected: 20999, shouldPass: false },
      { expected: 200, shouldPass: true },
    ];

    testCases.forEach(({ expected, shouldPass }) => {
      const testScript = `
pm.test("Status code test for ${expected}", function () {
    pm.response.to.have.status(${expected});
});
`;

      const results = executeTests(response200, testScript);
      
      console.log(`Testing status ${expected} against actual 200:`);
      console.log(`  Expected to pass: ${shouldPass}`);
      console.log(`  Actually passed: ${results[0].passed}`);
      console.log(`  Error: ${results[0].error || 'None'}`);
      console.log('');

      expect(results[0].passed).toBe(shouldPass);
      
      if (!shouldPass) {
        expect(results[0].error).toContain(`expected 200 to equal ${expected}`);
      }
    });
  });
});
