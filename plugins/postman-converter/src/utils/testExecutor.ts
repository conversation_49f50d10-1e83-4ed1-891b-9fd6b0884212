/**
 * Executes test scripts against a response
 */
import * as chai from 'chai';

export interface TestResult {
  id?: string;
  name: string;
  passed: boolean;
  error?: string;
  responseValue?: any;
  testType?: string;
  duration?: number;
}

export function executeTests(response: any, testScript: string): TestResult[] {
  if (!response || !testScript) return [];

  const results: TestResult[] = [];

  // Add debug logging to help track the issue
  console.log('🔍 executeTests called with:');
  console.log('  Response status:', response.status);
  console.log('  Test script:', testScript.substring(0, 200) + '...');

  try {
    // Create a sandbox environment to execute the test script
    const sandbox = createTestSandbox(response, results);

    // Execute the test script in the sandbox
    executeInSandbox(testScript, sandbox);

    console.log('✅ Test execution completed. Results:');
    results.forEach((result, index) => {
      console.log(`  Test ${index + 1}: ${result.name} - ${result.passed ? 'PASSED' : 'FAILED'}`);
      if (!result.passed && result.error) {
        console.log(`    Error: ${result.error}`);
      }
    });

    return results;
  } catch (error) {
    console.log('❌ Test execution failed with error:', error);
    // Handle global script errors (outside of pm.test calls)
    return [
      {
        id: `error-${Date.now()}`,
        name: "Script execution error",
        passed: false,
        error: error instanceof Error ? error.message : "Unknown error",
        testType: "error",
        duration: 0
      },
    ];
  }
}

function createTestSandbox(response: any, results: TestResult[]) {
  // Initialize chai for assertions
  const expect = chai.expect;

  // Create a mock response object similar to Postman's pm.response
  const mockResponse = {
    status: response.status,
    code: response.status,
    statusText: response.statusText,
    headers: {
      get: (name: string) => response.headers[name.toLowerCase()],
      has: (name: string) => name.toLowerCase() in response.headers,
    },
    responseTime: response.time,
    text: () => response.body || "",
    json: () => {
      try {
        return typeof response.body === "string"
          ? JSON.parse(response.body)
          : response.body;
      } catch (e) {
        throw new Error("Response body is not valid JSON");
      }
    },
    to: {
      have: {
        status: (code: number) => {
          expect(response.status).to.equal(code);
          return true;
        },
        header: (name: string) => name.toLowerCase() in response.headers,
      },
    },
  };

  // Create a mock pm object similar to Postman's pm
  const pm = {
    response: mockResponse,
    test: (name: string, testFn: () => void) => {
      // Create a proxy to intercept and capture values being tested
      const capturedValues: { [key: string]: any } = {};
      let testType = "unknown";
      const startTime = performance.now();

      try {

        // Create a proxy for the expect function to capture values
        const proxiedExpect = new Proxy(expect, {
          apply: function(target, thisArg, argumentsList) {
            // Capture the value being tested
            if (argumentsList.length > 0) {
              capturedValues.value = argumentsList[0];

              // Try to determine test type based on test name and value
              if (name.toLowerCase().includes("status")) {
                testType = "status";
                capturedValues.value = response.status;
              } else if (name.toLowerCase().includes("time")) {
                testType = "responseTime";
                capturedValues.value = response.time;
              } else if (name.toLowerCase().includes("header") || name.toLowerCase().includes("content-type")) {
                testType = "header";
                // Try to extract the header name from the test
                const headerMatch = name.match(/['"]([^'"]+)['"]/) || [];
                const headerName = headerMatch[1]?.toLowerCase();
                if (headerName && response.headers && response.headers[headerName]) {
                  capturedValues.value = response.headers[headerName];
                }
              } else if (typeof argumentsList[0] === "object") {
                testType = "json";

                // For JSON tests, try to extract the property being tested
                if (name.toLowerCase().includes("property")) {
                  const propertyMatch = name.match(/['"]([^'"]+)['"]/) || [];
                  const propertyName = propertyMatch[1];
                  if (propertyName && typeof response.body === "string") {
                    try {
                      const jsonData = JSON.parse(response.body);
                      if (propertyName in jsonData) {
                        capturedValues.value = jsonData[propertyName];
                      }
                    } catch (e) {
                      // Ignore JSON parsing errors
                    }
                  }
                }
              }
            }
            // Ensure we're passing the arguments correctly
            return target.apply(thisArg, argumentsList as [any, string?]);
          }
        });

        // Execute the test function with our proxied expect
        const originalExpect = pm.expect;
        pm.expect = proxiedExpect;
        testFn();
        pm.expect = originalExpect;

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // If no error was thrown, the test passed
        results.push({
          id: `test-${Date.now()}-${results.length}`,
          name,
          passed: true,
          responseValue: capturedValues.value,
          testType,
          duration
        });
      } catch (error) {
        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // If an error was thrown, the test failed
        results.push({
          id: `test-${Date.now()}-${results.length}`,
          name,
          passed: false,
          error: error instanceof Error ? error.message : "Test assertion failed",
          duration
        });
      }
    },
    // Use chai's expect for proper assertion chaining
    expect: expect
  };

  return { pm };
}

function executeInSandbox(script: string, sandbox: any) {
  // Create a function from the script and execute it with the sandbox as context
  try {
    // Create a function with the sandbox variables in scope
    const fn = new Function("pm", script);

    // Execute the function with the sandbox pm object
    fn(sandbox.pm);
  } catch (error) {
    // Re-throw the error to be caught by the main executeTests function
    throw error;
  }
}
