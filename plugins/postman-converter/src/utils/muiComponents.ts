/**
 * Enhanced MUI component utilities for better import management
 * This file provides organized component groups while maintaining ESLint compliance
 */

// Core Layout & Typography
export { default as Box } from '@mui/material/Box';
export { default as Container } from '@mui/material/Container';
export { default as Grid } from '@mui/material/Grid';
export { default as Stack } from '@mui/material/Stack';
export { default as Typography } from '@mui/material/Typography';
export { default as Divider } from '@mui/material/Divider';

// Buttons & Interactive Elements
export { default as Button } from '@mui/material/Button';
export { default as IconButton } from '@mui/material/IconButton';
export { default as Fab } from '@mui/material/Fab';
export { default as ButtonGroup } from '@mui/material/ButtonGroup';

// Form Components
export { default as TextField } from '@mui/material/TextField';
export { default as Select } from '@mui/material/Select';
export { default as MenuItem } from '@mui/material/MenuItem';
export { default as FormControl } from '@mui/material/FormControl';
export { default as FormControlLabel } from '@mui/material/FormControlLabel';
export { default as InputLabel } from '@mui/material/InputLabel';
export { default as Checkbox } from '@mui/material/Checkbox';
export { default as Radio } from '@mui/material/Radio';
export { default as RadioGroup } from '@mui/material/RadioGroup';
export { default as Switch } from '@mui/material/Switch';

// Data Display
export { default as Avatar } from '@mui/material/Avatar';
export { default as Chip } from '@mui/material/Chip';
export { default as Badge } from '@mui/material/Badge';
export { default as Tooltip } from '@mui/material/Tooltip';
export { default as List } from '@mui/material/List';
export { default as ListItem } from '@mui/material/ListItem';
export { default as ListItemText } from '@mui/material/ListItemText';
export { default as ListItemIcon } from '@mui/material/ListItemIcon';

// Cards & Surfaces
export { default as Card } from '@mui/material/Card';
export { default as CardContent } from '@mui/material/CardContent';
export { default as CardActions } from '@mui/material/CardActions';
export { default as CardHeader } from '@mui/material/CardHeader';
export { default as Paper } from '@mui/material/Paper';

// Navigation
export { default as Tabs } from '@mui/material/Tabs';
export { default as Tab } from '@mui/material/Tab';
export { default as AppBar } from '@mui/material/AppBar';
export { default as Toolbar } from '@mui/material/Toolbar';
export { default as Drawer } from '@mui/material/Drawer';
export { default as Breadcrumbs } from '@mui/material/Breadcrumbs';

// Feedback
export { default as Alert } from '@mui/material/Alert';
export { default as Snackbar } from '@mui/material/Snackbar';
export { default as CircularProgress } from '@mui/material/CircularProgress';
export { default as LinearProgress } from '@mui/material/LinearProgress';
export { default as Skeleton } from '@mui/material/Skeleton';

// Overlays
export { default as Dialog } from '@mui/material/Dialog';
export { default as DialogTitle } from '@mui/material/DialogTitle';
export { default as DialogContent } from '@mui/material/DialogContent';
export { default as DialogActions } from '@mui/material/DialogActions';
export { default as Modal } from '@mui/material/Modal';
export { default as Popover } from '@mui/material/Popover';

// Tables
export { default as Table } from '@mui/material/Table';
export { default as TableBody } from '@mui/material/TableBody';
export { default as TableCell } from '@mui/material/TableCell';
export { default as TableContainer } from '@mui/material/TableContainer';
export { default as TableHead } from '@mui/material/TableHead';
export { default as TableRow } from '@mui/material/TableRow';
export { default as TablePagination } from '@mui/material/TablePagination';

// Styles & Theme
export { styled, useTheme, alpha, darken, lighten } from '@mui/material/styles';
export type { Theme, SxProps } from '@mui/material/styles';

/**
 * Pre-configured component groups for common use cases
 */

// Table components bundle
export const TableComponents = {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
};

// Dialog components bundle
export const DialogComponents = {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
};

// Form components bundle
export const FormComponents = {
  TextField,
  Select,
  MenuItem,
  FormControl,
  FormControlLabel,
  InputLabel,
  Checkbox,
  Radio,
  RadioGroup,
  Switch,
};

// Layout components bundle
export const LayoutComponents = {
  Box,
  Container,
  Grid,
  Stack,
  Typography,
  Divider,
};

// Navigation components bundle
export const NavigationComponents = {
  Tabs,
  Tab,
  AppBar,
  Toolbar,
  Drawer,
  Breadcrumbs,
};

// Feedback components bundle
export const FeedbackComponents = {
  Alert,
  Snackbar,
  CircularProgress,
  LinearProgress,
  Skeleton,
};

/**
 * Utility functions for common MUI patterns
 */

// Common sx prop patterns
export const sxPatterns = {
  flexCenter: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  flexBetween: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  flexColumn: {
    display: 'flex',
    flexDirection: 'column',
  },
  textEllipsis: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  fullWidth: { width: '100%' },
  fullHeight: { height: '100%' },
};

// Common theme-based utilities
export const createThemeUtils = (theme: any) => ({
  spacing: theme.spacing,
  breakpoints: theme.breakpoints,
  palette: theme.palette,
  typography: theme.typography,
  shadows: theme.shadows,
  
  // Helper functions
  getSpacing: (multiplier: number) => theme.spacing(multiplier),
  getBreakpoint: (key: string) => theme.breakpoints.values[key],
  getPaletteColor: (color: string, shade?: string) => 
    shade ? theme.palette[color][shade] : theme.palette[color],
});

// HTTP method color mapping
export const httpMethodColors = {
  GET: '#4CAF50',
  POST: '#2196F3', 
  PUT: '#FF9800',
  DELETE: '#F44336',
  PATCH: '#9C27B0',
  HEAD: '#607D8B',
  OPTIONS: '#795548',
} as const;

// Status code color mapping
export const statusCodeColors = {
  1: '#2196F3', // 1xx Informational
  2: '#4CAF50', // 2xx Success
  3: '#FF9800', // 3xx Redirection
  4: '#F44336', // 4xx Client Error
  5: '#9C27B0', // 5xx Server Error
} as const;

/**
 * Enhanced import patterns for different component types
 */

// Example usage patterns:
/*
// Pattern 1: Individual imports (current approach - ESLint compliant)
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';

// Pattern 2: Grouped imports from this utility
import { Button, TextField, FormComponents } from '../../utils/muiComponents';

// Pattern 3: Component bundles
import { TableComponents, DialogComponents } from '../../utils/muiComponents';
const { Table, TableBody, TableCell } = TableComponents;

// Pattern 4: Theme utilities
import { useTheme, createThemeUtils, sxPatterns } from '../../utils/muiComponents';
const theme = useTheme();
const themeUtils = createThemeUtils(theme);
*/
