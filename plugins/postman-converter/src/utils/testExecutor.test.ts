import { executeTests } from './testExecutor';

describe('testExecutor', () => {
  const mockResponse = {
    status: 200,
    statusText: 'OK',
    headers: { 'content-type': 'application/json' },
    body: '{"message": "success"}',
    time: 150,
    size: 25
  };

  // Test the EXACT scenario the user is experiencing
  it('should FAIL when pm.response.to.have.status(20999) is run against status 200', () => {
    const testScript = `
pm.test("Status code should be 20999", function () {
    pm.response.to.have.status(20999);
});
`;

    console.log('Testing exact user scenario:');
    console.log('- Actual response status:', mockResponse.status);
    console.log('- Expected status in test:', 20999);
    console.log('- This should FAIL');

    const results = executeTests(mockResponse, testScript);

    console.log('Test execution results:', JSON.stringify(results, null, 2));

    // This test should FAIL because 200 !== 20999
    expect(results).toHaveLength(1);
    expect(results[0].passed).toBe(false);
    expect(results[0].name).toBe("Status code should be 20999");
    expect(results[0].error).toBeDefined();
    expect(results[0].error).toContain('20999');
  });

  it('should correctly fail when status code assertion is wrong', () => {
    const testScript = `
pm.test("Status code should be 20999 (this should fail)", function () {
    pm.response.to.have.status(20999);
});
`;

    const results = executeTests(mockResponse, testScript);

    expect(results).toHaveLength(1);
    expect(results[0].passed).toBe(false);
    expect(results[0].name).toBe("Status code should be 20999 (this should fail)");
    expect(results[0].error).toContain('expected 200 to equal 20999');
  });

  it('should correctly pass when status code assertion is correct', () => {
    const testScript = `
pm.test("Status code should be 200 (this should pass)", function () {
    pm.response.to.have.status(200);
});
`;

    const results = executeTests(mockResponse, testScript);
    
    expect(results).toHaveLength(1);
    expect(results[0].passed).toBe(true);
    expect(results[0].name).toBe("Status code should be 200 (this should pass)");
  });

  it('should handle multiple tests with mixed results', () => {
    const testScript = `
pm.test("Status code should be 20999 (this should fail)", function () {
    pm.response.to.have.status(20999);
});

pm.test("Status code should be 200 (this should pass)", function () {
    pm.response.to.have.status(200);
});
`;

    const results = executeTests(mockResponse, testScript);

    expect(results).toHaveLength(2);
    expect(results[0].passed).toBe(false);
    expect(results[1].passed).toBe(true);
  });

  it('should properly validate different status codes', () => {
    // Test with 404 response
    const notFoundResponse = {
      ...mockResponse,
      status: 404,
      statusText: 'Not Found'
    };

    const testScript404 = `
pm.test("Should fail for wrong status", function () {
    pm.response.to.have.status(200);
});

pm.test("Should pass for correct status", function () {
    pm.response.to.have.status(404);
});
`;

    const results = executeTests(notFoundResponse, testScript404);

    expect(results).toHaveLength(2);
    expect(results[0].passed).toBe(false);
    expect(results[0].error).toContain('expected 404 to equal 200');
    expect(results[1].passed).toBe(true);
  });

  it('should handle pm.expect assertions correctly', () => {
    const testScript = `
pm.test("Response time should be reasonable", function () {
    pm.expect(pm.response.responseTime).to.be.below(1000);
});

pm.test("Response time should fail for unrealistic expectation", function () {
    pm.expect(pm.response.responseTime).to.be.below(1);
});
`;

    const results = executeTests(mockResponse, testScript);

    expect(results).toHaveLength(2);
    expect(results[0].passed).toBe(true);
    expect(results[1].passed).toBe(false);
    expect(results[1].error).toContain('expected 150 to be below 1');
  });
});
