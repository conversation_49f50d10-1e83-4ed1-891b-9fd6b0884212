/**
 * Centralized MUI component re-exports for better import management
 * This file provides organized re-exports while maintaining ESLint compliance
 * with specific path imports from @mui/material
 */

// Core Layout Components
export { default as Box } from '@mui/material/Box';
export { default as Container } from '@mui/material/Container';
export { default as Grid } from '@mui/material/Grid';
export { default as Stack } from '@mui/material/Stack';
export { default as Divider } from '@mui/material/Divider';

// Typography Components
export { default as Typography } from '@mui/material/Typography';
export { default as Link } from '@mui/material/Link';

// Input Components
export { default as TextField } from '@mui/material/TextField';
export { default as Button } from '@mui/material/Button';
export { default as IconButton } from '@mui/material/IconButton';
export { default as Fab } from '@mui/material/Fab';
export { default as ButtonGroup } from '@mui/material/ButtonGroup';
export { default as ToggleButton } from '@mui/material/ToggleButton';
export { default as ToggleButtonGroup } from '@mui/material/ToggleButtonGroup';

// Form Components
export { default as FormControl } from '@mui/material/FormControl';
export { default as FormControlLabel } from '@mui/material/FormControlLabel';
export { default as FormGroup } from '@mui/material/FormGroup';
export { default as FormLabel } from '@mui/material/FormLabel';
export { default as FormHelperText } from '@mui/material/FormHelperText';
export { default as InputLabel } from '@mui/material/InputLabel';
export { default as OutlinedInput } from '@mui/material/OutlinedInput';
export { default as Input } from '@mui/material/Input';
export { default as InputAdornment } from '@mui/material/InputAdornment';

// Selection Components
export { default as Select } from '@mui/material/Select';
export { default as MenuItem } from '@mui/material/MenuItem';
export { default as MenuList } from '@mui/material/MenuList';
export { default as Menu } from '@mui/material/Menu';
export { default as Checkbox } from '@mui/material/Checkbox';
export { default as Radio } from '@mui/material/Radio';
export { default as RadioGroup } from '@mui/material/RadioGroup';
export { default as Switch } from '@mui/material/Switch';
export { default as Slider } from '@mui/material/Slider';

// Data Display Components
export { default as Avatar } from '@mui/material/Avatar';
export { default as AvatarGroup } from '@mui/material/AvatarGroup';
export { default as Badge } from '@mui/material/Badge';
export { default as Chip } from '@mui/material/Chip';
export { default as List } from '@mui/material/List';
export { default as ListItem } from '@mui/material/ListItem';
export { default as ListItemButton } from '@mui/material/ListItemButton';
export { default as ListItemIcon } from '@mui/material/ListItemIcon';
export { default as ListItemText } from '@mui/material/ListItemText';
export { default as ListItemAvatar } from '@mui/material/ListItemAvatar';
export { default as ListSubheader } from '@mui/material/ListSubheader';
export { default as Tooltip } from '@mui/material/Tooltip';

// Table Components
export { default as Table } from '@mui/material/Table';
export { default as TableBody } from '@mui/material/TableBody';
export { default as TableCell } from '@mui/material/TableCell';
export { default as TableContainer } from '@mui/material/TableContainer';
export { default as TableHead } from '@mui/material/TableHead';
export { default as TableRow } from '@mui/material/TableRow';
export { default as TableFooter } from '@mui/material/TableFooter';
export { default as TablePagination } from '@mui/material/TablePagination';
export { default as TableSortLabel } from '@mui/material/TableSortLabel';

// Card Components
export { default as Card } from '@mui/material/Card';
export { default as CardActions } from '@mui/material/CardActions';
export { default as CardContent } from '@mui/material/CardContent';
export { default as CardHeader } from '@mui/material/CardHeader';
export { default as CardMedia } from '@mui/material/CardMedia';

// Paper & Surface Components
export { default as Paper } from '@mui/material/Paper';
export { default as Accordion } from '@mui/material/Accordion';
export { default as AccordionActions } from '@mui/material/AccordionActions';
export { default as AccordionDetails } from '@mui/material/AccordionDetails';
export { default as AccordionSummary } from '@mui/material/AccordionSummary';

// Navigation Components
export { default as AppBar } from '@mui/material/AppBar';
export { default as Toolbar } from '@mui/material/Toolbar';
export { default as Tabs } from '@mui/material/Tabs';
export { default as Tab } from '@mui/material/Tab';
export { default as Breadcrumbs } from '@mui/material/Breadcrumbs';
export { default as Drawer } from '@mui/material/Drawer';
export { default as SwipeableDrawer } from '@mui/material/SwipeableDrawer';
export { default as BottomNavigation } from '@mui/material/BottomNavigation';
export { default as BottomNavigationAction } from '@mui/material/BottomNavigationAction';

// Feedback Components
export { default as Alert } from '@mui/material/Alert';
export { default as AlertTitle } from '@mui/material/AlertTitle';
export { default as Snackbar } from '@mui/material/Snackbar';
export { default as SnackbarContent } from '@mui/material/SnackbarContent';
export { default as CircularProgress } from '@mui/material/CircularProgress';
export { default as LinearProgress } from '@mui/material/LinearProgress';
export { default as Skeleton } from '@mui/material/Skeleton';
export { default as Backdrop } from '@mui/material/Backdrop';

// Dialog Components
export { default as Dialog } from '@mui/material/Dialog';
export { default as DialogActions } from '@mui/material/DialogActions';
export { default as DialogContent } from '@mui/material/DialogContent';
export { default as DialogContentText } from '@mui/material/DialogContentText';
export { default as DialogTitle } from '@mui/material/DialogTitle';

// Popover Components
export { default as Popover } from '@mui/material/Popover';
export { default as Popper } from '@mui/material/Popper';
export { default as Modal } from '@mui/material/Modal';

// Utility Components
export { default as ClickAwayListener } from '@mui/material/ClickAwayListener';
export { default as Portal } from '@mui/material/Portal';
export { default as TextareaAutosize } from '@mui/material/TextareaAutosize';
export { default as Zoom } from '@mui/material/Zoom';
export { default as Fade } from '@mui/material/Fade';
export { default as Grow } from '@mui/material/Grow';
export { default as Slide } from '@mui/material/Slide';
export { default as Collapse } from '@mui/material/Collapse';

// Styles and Theme
export { styled, useTheme, alpha, darken, lighten } from '@mui/material/styles';
export type { Theme, SxProps } from '@mui/material/styles';

// Common type exports
export type { 
  ButtonProps,
  TextFieldProps,
  TypographyProps,
  BoxProps,
  GridProps,
  CardProps,
  DialogProps,
  TableProps,
  ChipProps,
  AlertProps,
} from '@mui/material';

/**
 * Commonly used MUI component groups for easy importing
 */
export const MuiCore = {
  Box,
  Container,
  Grid,
  Stack,
  Typography,
  Button,
  TextField,
  Paper,
  Card,
  CardContent,
};

export const MuiTable = {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableFooter,
  TablePagination,
  TableSortLabel,
};

export const MuiDialog = {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
};

export const MuiForm = {
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  FormHelperText,
  TextField,
  Select,
  MenuItem,
  Checkbox,
  Radio,
  RadioGroup,
  Switch,
};

export const MuiNavigation = {
  AppBar,
  Toolbar,
  Tabs,
  Tab,
  Drawer,
  Breadcrumbs,
};

export const MuiFeedback = {
  Alert,
  AlertTitle,
  Snackbar,
  CircularProgress,
  LinearProgress,
  Skeleton,
};
