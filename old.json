{"info": {"_postman_id": "a582263e-9bb0-450e-873a-ef71d258dfe7", "name": "New THUB DEV Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "1481656", "_collection_link": "https://crimson-space-5978.postman.co/workspace/My-Workspace~29d33a3c-c243-42a5-855e-39236e2da1ee/collection/1481656-a582263e-9bb0-450e-873a-ef71d258dfe7?action=share&source=collection_link&creator=1481656"}, "item": [{"name": "THUB-Dev", "item": [{"name": "Transfers", "item": [{"name": "External Account Transfer", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "value": "en-US", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"recipient\": {\n        \"type\": \"BANK_ACCOUNT\",\n        \"bankAccountRecipient\": {\n            \"beneficiaryName\": \"EBC\",\n            \"bankId\": \"100\",\n            \"accountNumber\": \"****************\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 100,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"allam\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"]}}, "response": []}, {"name": "Internal Account Transfer", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "value": "ar-EG", "type": "default", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"recipient\": {\n        \"type\": \"BANK_ACCOUNT\",\n        \"bankAccountRecipient\": {\n            \"beneficiaryName\": \"ahmedz\",\n            \"bankId\": \"7\",\n            \"accountNumber\": \"************\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"allam\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"]}}, "response": []}, {"name": "Web Hook", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer ", "type": "default", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"status\":\"REJECTED\",\n    \"errorCode\":\"EIPN11001\",\n    \"errorDescription\":\"asdf\",\n    \"referenceNumber\":\"ABC123\"\n\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers/:globalId", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers", ":globalId"], "variable": [{"key": "globalId", "value": "6c6c594f-8c62-4850-8954-bb1421697812"}]}}, "response": []}, {"name": "Simulate Fees", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-tcnSoKHwT5XdIxk1gPGtdjIbnxJdRr4InQm2ctB2P0", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"recipient\":{\n        \"type\": \"BANK_ACCOUNT\",\n    \"bankAccountRecipient\": {\n        \"beneficiaryName\": \"tesa\",\n        \"bankId\": \"25\",\n        \"accountNumber\":\"************\"\n        }\n    },\n    \"nickname\": \"<PERSON>i\",\n    \"amount\": {\n        \"amount\": 2000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"RECIPIENT\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\":\"new test\",\n    \"otp\":\"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers/fees", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers", "fees"]}}, "response": []}, {"name": "Get all Transfers", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"], "query": [{"key": "pointer", "value": "MjAyNS0wMy0wMVQyMDoxMDo1Mi4yMzBa", "disabled": true}, {"key": "limit", "value": "2", "disabled": true}, {"key": "sortOrder", "value": "asc", "disabled": true}, {"key": "search", "value": "", "disabled": true}, {"key": "accountNumber", "value": "************", "disabled": true}, {"key": "toDate", "value": null, "disabled": true}, {"key": "fromDate", "value": "Wed+Feb+05+2025+15%3A12%3A20+GMT%2B0200+%28Eastern+European+Standard+Time%29", "disabled": true}, {"key": "fromAmount", "value": "0", "disabled": true}, {"key": "toAmount", "value": "0", "disabled": true}, {"key": "status", "value": "PENDING", "disabled": true}, {"key": "format", "value": "pdf", "disabled": true}]}}, "response": []}, {"name": "Get a certain transfer by transfer ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-tcnSoKHwT5XdIxk1gPGtdjIbnxJdRr4InQm2ctB2P0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers/:globalId", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers", ":globalId"], "variable": [{"key": "globalId", "value": ""}]}}, "response": []}, {"name": "IPN transfer request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{monamf_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Idempotency-Key", "value": "d154bb9d-0cf2-4029-9bb5-32e3d8dfbec4", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"recipient\": {\n        \"type\": \"INSTANT_PAYMENT_ADDRESS\",\n        \"instantPaymentAddressRecipient\": {\n            \"instantPaymentAddress\": \"testebc@demopay\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 5,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"]}}, "response": []}, {"name": "mobile number transfer request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{monamf_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"recipient\": {\n        \"type\": \"MOBILE_NUMBER\",\n        \"mobileNumberRecipient\": {\n            \"mobileNumber\": \"***********\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"]}}, "response": []}, {"name": "wallet mobile number transfer request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"recipient\": {\n        \"type\": \"WALLET_MOBILE_NUMBER\",\n        \"walletMobileNumberRecipient\": {\n            \"walletMobileNumber\": \"***********\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"]}}, "response": []}, {"name": "card transfer request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"recipient\": {\n        \"type\": \"CARD\",\n        \"cardRecipient\": {\n            \"cardNumber\": \"MIICEQYJKoZIhvcNAQcDoIICAjCCAf4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQAg5i2S3sYDhvNiShNxUA0csZARwmM/ACafhD4dPNJABVoHrT016cK058P8PL/03mNaNWXnjmH2bknyBqklgjVCNwbspYThzeUJKKYTpnb4eJVouc01evRlHaewg/ryyesFrx6wD142yZXui8Ur0oixaR6wrHqUT7t/cs9NYSh3X96C9/T4uDEOrxU97Q8rI3C1C590OTbQDFaIo3YloWKsk6juAxRl3WQvROh1160FjFlBUWyFDREu6u7p4o032uZN+GcwMSAgKS/SwxkkQGiDKWuqJVLH/fXNCKI4zAhpqkJdB93pjEJfJseDRvHaw9LCaLIvaF1mtUuW8j+Rek73ME4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEKFHYpUBuhRN9ucA5PANq/ygIgQgDRc2YtQQ01HLeRlDgj8m7ahmyhnqNFV6cm9dwvrpsKE=\",\n            \"cardHolderName\": \"EBC Test\"\n        }\n    },\n    \"nickname\": \"Doni\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"]}}, "response": []}, {"name": "IBAN transfer request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{monamf_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"recipient\": {\n        \"type\": \"BANK_ACCOUNT\",\n        \"ibanAccountRecipient\": {\n            \"beneficiaryName\": \"EBC IBAN\",\n            \"iban\": \"*****************************\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 500,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\":\"new test\",\n    \"otp\":\"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"]}}, "response": []}, {"name": "pay to existing card transfer request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"beneficiaryGlobalId\": \"07262b50-164a-41d0-924a-7811c415b0ad\",\n    \"recipient\": {\n        \"type\": \"CARD\",\n        \"cardRecipient\": {\n            \"cardNumber\": \"************6516\",\n            \"cardHolderName\": \"gazar visa\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON>\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId==\"\n    },\n    \"feesChargesOn\": \"SENDER\",\n    \"reasonForTransfer\": \"HousingFinancePayment\",\n    \"category\": \"other\",\n    \"description\": null,\n    \"isFavorite\": false,\n    \"note\": \"new test\",\n    \"otp\": \"111221\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfers", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfers"]}}, "response": []}]}, {"name": "Beneficiaries", "item": [{"name": "Create a new beneficiary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"cif\":\"121221\",\n     \"recipient\": {\n        \"type\": \"CARD\",\n        \"cardRecipient\": {\n            \"cardNumber\": \"MIICEQYJKoZIhvcNAQcDoIICAjCCAf4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQCpoZlUJdX2pVUz2Zdac0Rht/yo61FOQ0G93suo3fs7KZOTuaE92G6+SLaX3PlPvMnWgKpxk77TbfbSy1VY5am95guCMDpol5CUpOlP3eyvSJhe1XvvdViULGv2k9hmqVEDUsipwgu5OvFZgwVTbJLo/THas17lAMfCYU2VTNH98ZL1CsGSjvO3MHG9D8P2JvggU21OlxGoNMQe779qtXKa/CseVqcmKkDB6Z2jGmsu7NvrfGHIG/nByhluXKZM+6Lq/8TcLKUZxUzFlSFxgiQ9+sV4Gnv7catmWPAft82Uq9FIKs4qBixg1LeOvHFgiRb6v1bX7HMKQ1xX1ewIZa5BME4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEAaOwUoeLm9RQmi1qUtlN8KgIgQgVnTsQ1A7Q1mTaqyFtrZMNZxGWHw+9NPDfIJM/ESEyxk=\",\n            \"cardHolderName\": \"gazar visa\"\n        }\n    },\n    \"nickname\": \"blah vlah\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"RECIPIENT\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"category\": \"news\",\n    \"isFavorite\": true,\n    \"note\":\"Test\",\n    \"otp\":\"123311\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "beneficiaries"]}}, "response": []}, {"name": "Create a new beneficiary & pay", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"recipient\": {\n        \"type\": \"INSTANT_PAYMENT_ADDRESS\",\n        \"instantPaymentAddressRecipient\": {\n            \"instantPaymentAddress\": \"dinaebc123@demopay\"\n        }\n    },\n    \"nickname\": \"Donia 23\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"RECIPIENT\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"isNewCategory\": true,\n    \"category\": \"new category\",\n    \"isFavorite\": false,\n    \"note\": \"Test\",\n    \"otp\": \"123311\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries/pay", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "beneficiaries", "pay"]}}, "response": []}, {"name": "Get a certain beneficiary by beneficiary ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-tcnSoKHwT5XdIxk1gPGtdjIbnxJdRr4InQm2ctB2P0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries/:globalId", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "beneficiaries", ":globalId"], "variable": [{"key": "globalId", "value": ""}]}}, "response": []}, {"name": "Get All Beneficiaries", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{monamf_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries?limit=100&minAmount&maxAmount&fromDate&toDate", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "beneficiaries"], "query": [{"key": "pointer", "value": "MjAyNS0wMy0wNVQxNzoxMToyNC44Mzla", "disabled": true}, {"key": "limit", "value": "100"}, {"key": "sortOrder", "value": "asc", "disabled": true}, {"key": "isFavorite", "value": "true", "disabled": true}, {"key": "minAmount", "value": null}, {"key": "maxAmount", "value": null}, {"key": "fromDate", "value": null}, {"key": "toDate", "value": null}, {"key": "nickname", "value": "mohamed", "disabled": true}, {"key": "categoryId", "value": "ee7d9363-6aca-4557-bfb9-2cea7735feeb", "disabled": true}]}}, "response": []}, {"name": "Change favourite", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-tcnSoKHwT5XdIxk1gPGtdjIbnxJdRr4InQm2ctB2P0", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"isFavorite\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries/:globalId", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "beneficiaries", ":globalId"], "variable": [{"key": "globalId", "value": "b9253bfb-09b1-468a-8769-f4bf41f1bff1"}]}}, "response": []}, {"name": "Delete a certain beneficiary by beneficiary ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/beneficiaries/:globalId", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "beneficiaries", ":globalId"], "variable": [{"key": "globalId", "value": "db1e082e-63a8-4181-86f8-7ef92f5f4bbf"}]}}, "response": []}]}, {"name": "Categories", "item": [{"name": "Create a new category", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-tcnSoKHwT5XdIxk1gPGtdjIbnxJdRr4InQm2ctB2P0", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"categoryName\":\"testCat\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/categories", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "categories"]}}, "response": []}, {"name": "Get all categories", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n    \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/categories", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "categories"]}}, "response": []}, {"name": "Get a certain category by category ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-tcnSoKHwT5XdIxk1gPGtdjIbnxJdRr4InQm2ctB2P0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/categories/:globalId", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "categories", ":globalId"], "variable": [{"key": "globalId", "value": ""}]}}, "response": []}, {"name": "Update a certain category by category ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"categoryName\":\"updatedCat\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/categories/:globalId", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "categories", ":globalId"], "variable": [{"key": "globalId", "value": "32eef31e-3785-4539-8692-99ec7500b462"}]}}, "response": []}, {"name": "Delete a certain category by category ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/categories/:globalId", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "categories", ":globalId"], "variable": [{"key": "globalId", "value": "32eef31e-3785-4539-8692-99ec7500b462"}]}}, "response": []}]}, {"name": "Others", "item": [{"name": "IPA Inquiry", "request": {"method": "POST", "header": [{"key": "Accept-Language", "value": "ar-EG", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"identifier\":\"daainaebc123\",\n    \"type\": \"MOBILE_NUMBER\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/request-name", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "request-name"]}}, "response": []}, {"name": "Get Banks List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-tcnSoKHwT5XdIxk1gPGtdjIbnxJdRr4InQm2ctB2P0", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "ar-EG", "type": "default"}], "url": {"raw": "{{base_url}}/v1/thub/ipn/banks", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "banks"]}}, "response": []}, {"name": "Get Transfer Reasons", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-tcnSoKHwT5XdIxk1gPGtdjIbnxJdRr4InQm2ctB2P0", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "ar-EG", "type": "default"}], "body": {"mode": "raw", "raw": "{\n    \"cif\":\"121221\",\n    \"recipient\":{\n        \"type\": \"CARD\",\n    \"cardRecipient\": {\n        \"cardNumber\": \"****************\",\n        \"cardHolderName\": \"<PERSON><PERSON>\"\n        }\n    },\n    \"nickname\": \"<PERSON><PERSON> 23\",\n    \"amount\": {\n        \"amount\": 1000,\n        \"currency\": \"EGP\"\n    },\n    \"sender\": {\n        \"accountNumber\": \"************\",\n        \"accountId\": \"myEncryptedId\"\n    },\n    \"feesChargesOn\": \"RECIPIENT\",\n    \"reasonForTransfer\": \"Family Support\",\n    \"isNewCategory\":true,\n    \"category\": \"new category\",\n    \"isFavorite\": false,\n    \"note\":\"Test\",\n    \"otp\":\"123311\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/transfer-reasons", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "transfer-reasons"]}}, "response": []}, {"name": "Decrypt message", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "MIICEQYJKoZIhvcNAQcDoIICAjCCAf4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQBZJKnEW98d3Q1rLErruMi58CeWhZqHot2m/4HCJ0gq+gF3NuDG2qi2IDNqEMXDShqK4aOWn96OtCFCK6n00iL/orGAIrNxErz/iRE1zARpWcfxwG3N/z30BAJORbKS7cuG0raN3tqup3XpLECGiRVQq/6ZseF/s3QDQ3SfxJkLWDIq63ETR4Rzdt5vmd/h3pOhjYtA8+mjNbAUor//1quOcGUxpAodtcH2t9BnO+vN9pez5DWc84htY7DT4HI44o9j82qPiS9w4fvwxFWd2Av5RiWqGZTJilpZr5QZ3rk3371VLuaobcy2So2nCpdczkopI0a+KHghmmsuU6T2+cqVME4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEEgR5XDLFexpa8mlk73w14KgIgQgOOXimPIK02S/j2YMEV5UA9qNHsuL28DNvdje6A3GUAY=\n", "options": {"raw": {"language": "text"}}}, "url": {"raw": "{{base_url}}/v1/thub/ipn/decrypt-message", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "decrypt-message"]}}, "response": []}, {"name": "Get dummy message encrypted", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/ipn/encrypted-message", "host": ["{{base_url}}"], "path": ["v1", "thub", "ipn", "encrypted-message"]}}, "response": []}]}, {"name": "Limits", "item": [{"name": "Usage", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.hBiOaWg5ULu3kn97BpW84KoDP3tM0xpSdH9fhFgY1CA", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/thub/limits/usage", "host": ["{{base_url}}"], "path": ["v1", "thub", "limits", "usage"]}}, "response": []}, {"name": "checkLimits", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"amount\":1000,\n    \"transferType\":\"IPN\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/limits/check", "host": ["{{base_url}}"], "path": ["v1", "thub", "limits", "check"]}}, "response": []}, {"name": "addTransfer", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"amount\":1000,\n    \"transferType\":\"IPN\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/limits/add", "host": ["{{base_url}}"], "path": ["v1", "thub", "limits", "add"]}}, "response": []}, {"name": "rollbackTransfer", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"amount\":1000,\n    \"transferType\":\"IPN\",\n    \"date\":\"Wed+Feb+05+2025+15%3A12%3A20+GMT%2B0200+%28Eastern+European+Standard+Time%29\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/thub/limits/rollback", "host": ["{{base_url}}"], "path": ["v1", "thub", "limits", "rollback"]}}, "response": []}]}]}]}