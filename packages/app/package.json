{"name": "app", "version": "0.0.0", "private": true, "bundled": true, "backstage": {"role": "frontend"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "clean": "backstage-cli package clean", "test": "backstage-cli package test", "lint": "backstage-cli package lint"}, "dependencies": {"@backstage/app-defaults": "^1.6.1", "@backstage/canon": "^0.3.0", "@backstage/catalog-model": "^1.7.3", "@backstage/cli": "^0.32.0", "@backstage/core-app-api": "^1.16.1", "@backstage/core-components": "^0.17.1", "@backstage/core-plugin-api": "^1.10.6", "@backstage/integration-react": "^1.2.6", "@backstage/plugin-api-docs": "^0.12.6", "@backstage/plugin-catalog": "^1.29.0", "@backstage/plugin-catalog-common": "^1.1.3", "@backstage/plugin-catalog-graph": "^0.4.18", "@backstage/plugin-catalog-import": "^0.12.13", "@backstage/plugin-catalog-react": "^1.17.0", "@backstage/plugin-kubernetes": "^0.12.6", "@backstage/plugin-org": "^0.6.38", "@backstage/plugin-permission-react": "^0.4.33", "@backstage/plugin-scaffolder": "^1.30.0", "@backstage/plugin-search": "^1.4.25", "@backstage/plugin-search-react": "^1.8.8", "@backstage/plugin-techdocs": "^1.12.5", "@backstage/plugin-techdocs-module-addons-contrib": "^1.1.23", "@backstage/plugin-techdocs-react": "^1.2.16", "@backstage/plugin-user-settings": "^0.8.21", "@backstage/theme": "^0.6.5", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@internal/plugin-features": "workspace:^", "@internal/plugin-postman-converter": "workspace:^", "@internal/plugin-product": "workspace:^", "@mui/icons-material": "^5.15.0", "@mui/material": "^5.15.0", "@mui/styles": "^5.15.0", "react": "^18.0.2", "react-dom": "^18.0.2", "react-router": "^6.3.0", "react-router-dom": "^6.3.0"}, "devDependencies": {"@backstage/test-utils": "^1.7.7", "@playwright/test": "^1.32.3", "@testing-library/dom": "^9.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/react-dom": "*", "cross-env": "^7.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "files": ["dist"]}